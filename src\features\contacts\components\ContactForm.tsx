import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper, Select, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import validator from "validator";
import i18next from "i18next";
import { getDirtyFormFields } from "@/features/entity/utils";

import { getEnumTransKey } from "@/utils/trans";
import { Salutation } from "@/types/enums";
import { ReservationList } from "@/features/reservations/routes/ReservationList";
import { LeadList } from "@/features/leads/routes/LeadList";
import { CustomerContactList } from "@/features/customerContacts/routes/CustomerContactList";
import { CaseList } from "@/features/cases/routes/CaseList";

const formSchema = z.object({
  id: z.string(),
  number: z.string(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  salutation: z.enum(Salutation as [string]).nullable(),
  email: z.string().refine(
    (value) => {
      if (value) {
        return validator.isEmail(value);
      } else {
        return true;
      }
    },
    { message: i18next.t("common:validation.invalidEmail") },
  ),
  mobile: z
    .string()
    .min(1)
    .refine(
      (value) => {
        if (value) {
          return validator.isMobilePhone(value);
        } else {
          return true;
        }
      },
      { message: i18next.t("common:validation.invalidPhone") },
    ),
  phone: z.string().refine(
    (value) => {
      if (value) {
        return validator.isMobilePhone(value);
      } else {
        return true;
      }
    },
    { message: i18next.t("common:validation.invalidPhone") },
  ),
  identificationNumber: z.string(),
});

type FormSchema = z.infer<typeof formSchema>;

interface ContactFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: FormSchema;
  title: string;
  isCreate: boolean;
}

export function ContactForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: ContactFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      number: initialValues?.number ?? "",
      firstName: initialValues?.firstName ?? "",
      lastName: initialValues?.lastName ?? "",
      salutation: initialValues?.salutation ?? "Mx",
      email: initialValues?.email ?? "",
      mobile: initialValues?.mobile ?? "",
      phone: initialValues?.phone ?? "",
      identificationNumber: initialValues?.identificationNumber ?? "",
    },
    validate: zodResolver(formSchema),
  });
  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Paper shadow="xs" p="xs" pt="">
              <TextInput
                disabled
                label={t("contacts.number")}
                {...form.getInputProps("number")}
              />
              <TextInput
                required
                label={t("contacts.firstName")}
                {...form.getInputProps("firstName")}
              />
              <TextInput
                required
                label={t("contacts.lastName")}
                {...form.getInputProps("lastName")}
              />
              <Select
                searchable
                label={t("leads.salutation")}
                data={Salutation.map((value) => ({
                  value,
                  label: t(getEnumTransKey("leads", value)),
                }))}
                clearable
                {...form.getInputProps("salutation")}
              />
              <TextInput
                required
                label={t("contacts.email")}
                {...form.getInputProps("email")}
              />
              <TextInput
                required
                label={t("contacts.mobile")}
                {...form.getInputProps("mobile")}
              />
              <TextInput
                label={t("contacts.phone")}
                {...form.getInputProps("phone")}
              />
              <TextInput
                label={t("contacts.identificationNumber")}
                {...form.getInputProps("identificationNumber")}
              />
            </Paper>
          </Grid.Col>
          {initialValues?.id ? (
            <>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <LeadList
                  parentEntityId={initialValues?.id}
                  parentEntityName="Contacts"
                  parentEntityIdParam="contactId"
                  visibleColumns={[
                    "contract",
                    "businessUnit",
                    "processStage",
                    "recordState",
                    "createdOn",
                  ]}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <ReservationList
                  parentEntityId={initialValues?.id}
                  parentEntityName="Contacts"
                  parentEntityIdParam="contactId"
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <CustomerContactList
                  parentEntityId={initialValues?.id}
                  parentEntityName="contacts"
                  parentEntityIdParam="contactId"
                  visibleColumns={["customer", "contactRole"]}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 12 }}>
                <CaseList
                  parentEntityId={initialValues?.id}
                  parentEntityName="contacts"
                  parentEntityIdParam="contactId"
                />
              </Grid.Col>
            </>
          ) : null}
        </Grid>
      </EntityLayout>
    </form>
  );
}
