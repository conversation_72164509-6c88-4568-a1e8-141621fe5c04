import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import {
  Box,
  Grid,
  NumberInput,
  Paper,
  Select,
  Stack,
  TextInput,
  Text,
} from "@mantine/core";
import { type ReactNode } from "react";
import { config } from "@/config";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields } from "@/features/entity/utils";
import { RentableItemType, FuelType } from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { AppointmentList } from "@/features/appointments/routes/AppointmentList";

const baseFormSchema = z.object({
  id: z.string(),
  brand: z.string().nullable(),
  model: z.string().nullable(),
  licensePlate: z.string().nullable(),
  loadingSpaceLength: z.coerce.number().nullable(),
  loadingSpaceHeight: z.coerce.number().nullable(),
  loadingSpaceWidth: z.coerce.number().nullable(),
  loadingSpaceVolume: z.coerce.number().nullable(),
  maxLoad: z.coerce.number().nullable(),
  businessUnitId: z.string(),
  businessUnit: z.object({}).nullable(),
  type: z.enum(RentableItemType as [string]),
  fuelType: z.enum(FuelType as [string]),
});

export type FormSchema = z.infer<typeof baseFormSchema>;

interface RentableItemFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function RentableItemForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: RentableItemFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      id: initialValues?.id ?? "",
      brand: initialValues?.brand ?? "",
      model: initialValues?.model ?? "",
      licensePlate: initialValues?.licensePlate ?? "",
      loadingSpaceLength: initialValues?.loadingSpaceLength ?? null,
      loadingSpaceHeight: initialValues?.loadingSpaceHeight ?? null,
      loadingSpaceWidth: initialValues?.loadingSpaceWidth ?? null,
      loadingSpaceVolume: initialValues?.loadingSpaceVolume ?? null,
      maxLoad: initialValues?.maxLoad ?? null,
      businessUnitId: initialValues?.businessUnitId ?? "",
      businessUnit: initialValues?.businessUnit ?? null,
      type: initialValues?.type ?? "",
      fuelType: initialValues?.fuelType ?? "",
    },
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        actionSection={actionSection}
      >
        <Grid m={16}>
          <Grid.Col span={{ base: 12, md: 4 }}>
            <Paper shadow="xs" p="lg" h="100%">
              <FieldValidation isDirty={form.isDirty("type")}>
                <Select
                  searchable
                  required
                  label={t("rentableItems.type")}
                  data={RentableItemType.map((value) => ({
                    value,
                    label: t(getEnumTransKey("rentableItems", value)),
                  }))}
                  clearable
                  {...form.getInputProps("type")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("brand")}>
                <TextInput
                  label={t("rentableItems.brand")}
                  {...form.getInputProps("brand")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("model")}>
                <TextInput
                  label={t("rentableItems.model")}
                  {...form.getInputProps("model")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("licensePlate")}>
                <TextInput
                  label={t("rentableItems.licensePlate")}
                  {...form.getInputProps("licensePlate")}
                />
              </FieldValidation>
              <BusinessUnitLookup
                label={t("rentableItems.businessUnit")}
                initial={form.getValues().businessUnit}
                initialId={form.getValues().businessUnitId}
                identifier="businessUnitIdRentableItem"
                required
                {...form.getInputProps("businessUnitId")}
              />
              <FieldValidation isDirty={form.isDirty("fuelType")}>
                <Select
                  searchable
                  label={t("rentableItems.fuelType")}
                  data={FuelType.map((value) => ({
                    value,
                    label: t(getEnumTransKey("rentableItems", value)),
                  }))}
                  clearable
                  {...form.getInputProps("fuelType")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 4 }}>
            <Stack gap="lg" h={"100%"}>
              <Paper shadow="xs" p="lg" h="100%">
                <Text size="sm" fw={700} mb={8}>
                  {t("rentableItems.loadingSpaceSection")}
                </Text>
                <FieldValidation isDirty={form.isDirty("loadingSpaceLength")}>
                  <NumberInput
                    label={t("rentableItems.loadingSpaceLength")}
                    leftSection={config.METERS.symbol}
                    {...form.getInputProps("loadingSpaceLength")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("loadingSpaceHeight")}>
                  <NumberInput
                    label={t("rentableItems.loadingSpaceHeight")}
                    leftSection={config.METERS.symbol}
                    {...form.getInputProps("loadingSpaceHeight")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("loadingSpaceWidth")}>
                  <NumberInput
                    label={t("rentableItems.loadingSpaceWidth")}
                    leftSection={config.METERS.symbol}
                    {...form.getInputProps("loadingSpaceWidth")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("loadingSpaceVolume")}>
                  <NumberInput
                    label={t("rentableItems.loadingSpaceVolume")}
                    leftSection={config.CUBICMETERS.symbol}
                    {...form.getInputProps("loadingSpaceVolume")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("maxLoad")}>
                  <NumberInput
                    label={t("rentableItems.maxLoad")}
                    leftSection={config.KILOGRAMS.symbol}
                    {...form.getInputProps("maxLoad")}
                  />
                </FieldValidation>
              </Paper>
            </Stack>
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 12 }}>
            {!isCreate && (
              <Box mb={6}>
                <AppointmentList
                  parentEntityId={initialValues?.id}
                  parentEntityName="RentableItems"
                  parentEntityIdParam="rentableItemId"
                  visibleColumns={[
                    "appointmentType",
                    "appointmentStatus",
                    "startDate",
                    "endDate",
                  ]}
                  businessUnitId={form.getValues().businessUnitId ?? ""}
                />
              </Box>
            )}
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
