import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import {
  Grid,
  Group,
  NumberInput,
  Paper,
  Select,
  Switch,
  TextInput,
  Textarea,
  Text,
  Fieldset,
  Stack,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import {
  LeadType,
  Salutation,
  StartWithin,
  LeadSource,
  PreferredLanguage,
  OptInType,
  ProductType,
} from "@/types/enums";
import { getEnumTransKey } from "@/utils/trans";
import { useLeadFormContext, type LeadFormSchema } from "../../providers/form";
import { type UseFormReturnType } from "@mantine/form";
import { AdvisedProductList } from "@/features/advisedProducts/routes/AdvisedProductList";
import { AdvisedUnitList } from "@/features/advisedUnits/routes/AdvisedUnitList";
import { ReservationList } from "@/features/reservations/routes/ReservationList";
import { QuoteList } from "@/features/quotes/routes/QuoteList";
import { DateInput, DatePickerInput } from "@mantine/dates";
import { TimeLine } from "@/components/TimeLine/TimeLine";
import { ContactLookup } from "@/components/Lookup/Features/Contacts/ContactLookup";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { CustomerLookup } from "@/components/Lookup/Features/Customers/CustomerLookup";
import { CountryLookup } from "@/components/Lookup/Features/Countries/CountryLookup";
import { useSettingsContext } from "@/components/Layout/Contexts/Settings/useSettingsContext";
import { ContractList } from "@/features/contracts/routes/ContractList";

interface GeneralTabProps {
  leadId?: string;
}

export function GeneralTab({ leadId }: GeneralTabProps) {
  const { dateFormat } = useSettingsContext();
  const { t } = useTranslation("features");
  const form = useLeadFormContext() as UseFormReturnType<LeadFormSchema>;
  const formValues = form.getValues();
  const isFormDisabled = form.getInputProps("recordState").value === "Inactive";

  return (
    <>
      <Fieldset disabled={isFormDisabled}>
        <Grid m={16}>
          <Grid.Col span={{ base: 6, md: 4 }}>
            <Paper shadow="xs" p="lg" h="100%">
              <FieldValidation isDirty={form.isDirty("firstName")}>
                <TextInput
                  required
                  label={t("leads.firstName")}
                  {...form.getInputProps("firstName")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("lastName")}>
                <TextInput
                  required
                  label={t("leads.lastName")}
                  {...form.getInputProps("lastName")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("salutation")}>
                <Select
                  searchable
                  label={t("leads.salutation")}
                  data={Salutation.map((value) => ({
                    value,
                    label: t(getEnumTransKey("leads", value)),
                  }))}
                  clearable
                  rightSectionPointerEvents={isFormDisabled ? "none" : "auto"}
                  {...form.getInputProps("salutation")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("email")}>
                <TextInput
                  required
                  label={t("leads.email")}
                  {...form.getInputProps("email")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("mobile")}>
                <TextInput
                  required
                  label={t("leads.mobile")}
                  {...form.getInputProps("mobile")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("phone")}>
                <TextInput
                  label={t("leads.phone")}
                  {...form.getInputProps("phone")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("companyName")}>
                <TextInput
                  required={form.getInputProps("type").value === "Business"}
                  label={t("leads.companyName")}
                  {...form.getInputProps("companyName")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("type")}>
                <Select
                  searchable
                  label={t("leads.type")}
                  data={LeadType.map((value) => ({
                    value,
                    label: t(getEnumTransKey("leads", value)),
                  }))}
                  clearable
                  rightSectionPointerEvents={isFormDisabled ? "none" : "auto"}
                  {...form.getInputProps("type")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("startWithin")}>
                <Select
                  searchable
                  label={t("leads.startWithin")}
                  data={StartWithin.map((value) => ({
                    value,
                    label: t(getEnumTransKey("leads", value)),
                  }))}
                  clearable
                  rightSectionPointerEvents={isFormDisabled ? "none" : "auto"}
                  {...form.getInputProps("startWithin")}
                />
              </FieldValidation>
              <TextInput
                disabled
                label={t("leads.promotionCode")}
                {...form.getInputProps("promotionCode")}
              />
              <FieldValidation isDirty={form.isDirty("description")}>
                <Textarea
                  label={t("leads.description")}
                  {...form.getInputProps("description")}
                  minRows={4}
                  autosize
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("comments")}>
                <Textarea
                  label={t("leads.comments")}
                  {...form.getInputProps("comments")}
                  minRows={4}
                  autosize
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
          <Grid.Col span={{ base: 6, md: 4 }}>
            <Paper shadow="xs" p="lg" h="100%">
              <TextInput
                disabled
                label={t("leads.webformTitle")}
                {...form.getInputProps("webformTitle")}
              />
              <TextInput
                disabled
                label={t("leads.webformDetails")}
                {...form.getInputProps("webformDetails")}
              />
              <DateInput
                disabled
                valueFormat={dateFormat.toUpperCase() + " HH:mm"}
                label={t("leads.makeAnAppointment")}
                {...form.getInputProps("makeAnAppointment")}
              />
              <TextInput
                disabled
                label={t("leads.step")}
                {...form.getInputProps("step")}
              />
              <TextInput
                disabled
                label={t("leads.price")}
                {...form.getInputProps("price")}
              />
              <TextInput
                disabled
                label={t("leads.unitSize")}
                {...form.getInputProps("unitSize")}
              />
              <FieldValidation isDirty={form.isDirty("productType")}>
                <Select
                  required
                  searchable
                  label={t("leads.productType")}
                  data={ProductType.map((value) => ({
                    value,
                    label: t(getEnumTransKey("leads", value)),
                  }))}
                  {...form.getInputProps("productType")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("leadSource")}>
                <Select
                  required
                  searchable
                  label={t("leads.leadSource")}
                  data={LeadSource.map((value) => ({
                    value,
                    label: t(getEnumTransKey("leads", value)),
                  }))}
                  clearable
                  rightSectionPointerEvents={isFormDisabled ? "none" : "auto"}
                  {...form.getInputProps("leadSource")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("preferredLanguage")}>
                <Select
                  searchable
                  label={t("leads.preferredLanguage")}
                  data={PreferredLanguage.map((value) => ({
                    value,
                    label: t(getEnumTransKey("leads", value)),
                  }))}
                  clearable
                  rightSectionPointerEvents={isFormDisabled ? "none" : "auto"}
                  {...form.getInputProps("preferredLanguage")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("businessUnitId")}>
                <BusinessUnitLookup
                  required
                  initial={formValues?.businessUnit}
                  initialId={formValues?.businessUnitId}
                  identifier="businessUnitIdLead"
                  label={t("leads.businessUnit")}
                  {...form.getInputProps("businessUnitId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("existingContactId")}>
                <ContactLookup
                  initial={formValues?.existingContact}
                  initialId={formValues?.existingContactId}
                  identifier="existingContactIdLead"
                  mt="sm"
                  label={t("leads.existingContactId")}
                  {...form.getInputProps("existingContactId")}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("existingCustomerId")}>
                <CustomerLookup
                  initial={formValues?.existingCustomer}
                  initialId={formValues?.existingCustomerId}
                  identifier="existingCustomerIdLead"
                  label={t("leads.existingCustomerId")}
                  {...form.getInputProps("existingCustomerId")}
                />
              </FieldValidation>
            </Paper>
          </Grid.Col>
          <Grid.Col span={{ base: 6, md: 4 }}>
            <Stack gap="lg" h={"100%"}>
              <Paper shadow="xs" p="lg">
                <Text size="sm" fw={600} mb={8}>
                  {t("leads.addressSection")}
                </Text>
                <FieldValidation isDirty={form.isDirty("fax")}>
                  <TextInput
                    label={t("leads.fax")}
                    {...form.getInputProps("fax")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("city")}>
                  <TextInput
                    label={t("leads.city")}
                    {...form.getInputProps("city")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("street")}>
                  <TextInput
                    label={t("leads.street")}
                    {...form.getInputProps("street")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("zip")}>
                  <TextInput
                    label={t("leads.zip")}
                    {...form.getInputProps("zip")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("countryId")}>
                  <CountryLookup
                    initial={formValues?.country}
                    initialId={formValues?.countryId}
                    identifier="countryIdLead"
                    label={t("leads.country")}
                    {...form.getInputProps("countryId")}
                    shiftLeft="78vw"
                  />
                </FieldValidation>
              </Paper>
              <Paper shadow="xs" p="lg" h="100%">
                <Text size="sm" fw={700} mb={8}>
                  {t("leads.callSection")}
                </Text>
                <NumberInput
                  disabled
                  hideControls
                  label={t("leads.callCount")}
                  {...form.getInputProps("callCount")}
                />
                <NumberInput
                  disabled
                  hideControls
                  label={t("leads.callCountReached")}
                  {...form.getInputProps("callCountReached")}
                />
                <NumberInput
                  disabled
                  hideControls
                  label={t("leads.callCountNotReached")}
                  {...form.getInputProps("callCountNotReached")}
                />
                <DatePickerInput
                  value={
                    formValues.nextCallback
                      ? new Date(formValues.nextCallback ?? "")
                      : null
                  }
                  valueFormat={`${dateFormat.toUpperCase()} HH:mm`}
                  disabled
                  label={t("leads.nextCallback")}
                />
                <FieldValidation isDirty={form.isDirty("optInType")}>
                  <Select
                    searchable
                    label={t("leads.optInType")}
                    data={OptInType.map((value) => ({
                      value,
                      label: t(getEnumTransKey("leads", value)),
                    }))}
                    clearable
                    rightSectionPointerEvents={isFormDisabled ? "none" : "auto"}
                    {...form.getInputProps("optInType")}
                  />
                </FieldValidation>
                <FieldValidation isDirty={form.isDirty("optOutPhone")}>
                  <Group gap="lg" mt="xs">
                    <Switch
                      checked={form.getValues().optOutPhone}
                      label={t("leads.optOutPhone")}
                      {...form.getInputProps("optOutPhone")}
                    />
                    <Switch
                      checked={form.getValues().approvalForAddressUsage}
                      label={t("leads.approvalForAddressUsage")}
                      {...form.getInputProps("approvalForAddressUsage")}
                    />
                  </Group>
                </FieldValidation>
              </Paper>
            </Stack>
          </Grid.Col>
        </Grid>
      </Fieldset>

      {leadId ? (
        <Grid mt="lg">
          <Grid.Col span={6}>
            <Paper shadow="xs" p="lg" pt="">
              <TimeLine
                timeLineEntity="Leads"
                timeLineEntityId={leadId}
                sendEmailTo={form.getValues().email}
                businessUnitId={form.getValues().businessUnitId ?? undefined}
                queryKey="leadActivity"
              />
            </Paper>
          </Grid.Col>

          <Grid.Col span={6}>
            <Fieldset disabled={isFormDisabled}>
              <AdvisedUnitList
                visibleColumns={[
                  "unit",
                  "unit.volume",
                  "pricePerMonth",
                  "businessUnit",
                  "createdOn",
                  "createdByUser",
                ]}
                parentEntityId={leadId}
                parentEntityName="Leads"
                parentEntityIdParam="leadId"
              />
              <AdvisedProductList
                visibleColumns={["price", "product", "quantity", "totalPrice"]}
                parentEntityId={leadId}
                parentEntityName="Leads"
                parentEntityIdParam="leadId"
              />
              <QuoteList
                parentEntityId={leadId}
                parentEntityName="Leads"
                parentEntityIdParam="leadId"
                visibleColumns={[
                  "number",
                  "totalMonthlyPrice",
                  "expirationDate",
                  "status",
                  "createdOn",
                  "owner",
                ]}
              />
              <ReservationList
                parentEntityId={leadId}
                parentEntityName="Leads"
                parentEntityIdParam="leadId"
              />
            </Fieldset>
          </Grid.Col>
        </Grid>
      ) : null}
    </>
  );
}
