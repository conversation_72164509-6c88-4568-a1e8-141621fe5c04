import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { useEntityUpdateMutation } from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { LeadForm } from "../components/LeadForm";
import { Group } from "@mantine/core";
import { EntityLayout } from "@/features/entity";

import { PageLoader } from "@/components/PageLoader";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";
import { LeadLostButton } from "../components/LeadLostButton";
import { SendQuoteButton } from "../components/SendQuoteButton";
import { ReservationButton } from "../components/ReservationButton";
import { ReOpenButton } from "../components/ReOpenButton";
import { type LeadFormSchema } from "../providers/form";
import { useUserContext } from "@/components/Layout/Contexts/User/useUserContext";
import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { IconPhone } from "@tabler/icons-react";
import { useLayoutVisibility } from "@/components/Layout/Contexts/LayoutVisibility/LayoutVisibilityContext";
import { addLeadToRecent } from "../localStorage/leadStorage";
import LeadContractButton from "../components/LeadContractButton";

export function LeadShow() {
  const queryCache = useQueryClient();
  const { setOCLeadValue } = useUserContext();
  const [searchParams] = useSearchParams();
  const { activeNavbar } = useLayoutVisibility();
  const isSalesAndServiceApp = activeNavbar === "Sales & Service";
  const redirectTo =
    searchParams.get("redirectTo") ?? isSalesAndServiceApp
      ? "/app/omnichannel"
      : "/app/leads";
  const navigate = useNavigate();
  const [close, setClose] = useState(false);
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();

  useEffect(() => {
    setOCLeadValue(id!);
  }, [id, setOCLeadValue]);

  const { mutate: update } = useEntityUpdateMutation<
    Schemas["Lead"],
    Schemas["LeadPatchDto"]
  >({ resourcePath: "/api/Leads/{id}", resourceId: id!, queryKey: "lead" });

  const oneYearAgo = new Date();
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["Lead"]>({
    resourcePath: "/api/Leads/{id}",
    resourceId: id!,
    queryKey: "lead",
  });

  const refreshForm = async () => {
    await queryCache.invalidateQueries("lead_" + id);
  };

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  addLeadToRecent(data);

  return (
    <LeadForm
      isCreate={false}
      title={
        t("leads.showTitle") +
        (data.lastName ? " - " + (data.lastName + " " + data.firstName) : "")
      }
      leadId={data.id}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            if (redirectTo) {
              navigate(redirectTo);
            } else {
              navigate(
                isSalesAndServiceApp ? "/app/omnichannel" : "/app/leads",
              );
            }
          } else {
            return;
          }
        }
        const filteredValues = filterFalsyValues(values);
        update(
          { ...filteredValues },
          {
            onSuccess: () => {
              void queryCache.invalidateQueries("reservation_list");
              if (close) {
                navigate(
                  isSalesAndServiceApp ? "/app/omnichannel" : "/app/leads",
                );
              } else {
                notifications.show({
                  color: "green",
                  title: t("notifications.updateSuccessTitle"),
                  message: t("notifications.updateSuccessMessage"),
                });
              }
            },
          },
        );
      }}
      updateProcessStage={(stage) => {
        update(
          {
            processStage: stage,
            ...(stage === "Won" && { recordState: "Inactive" }),
          },
          {
            onSuccess: () => {
              notifications.show({
                color: "green",
                title: t("leads.updateProcessStageSuccessTitle"),
                message: t("leads.updateProcessStageSuccessMessage"),
              });
            },
            onError: () => {
              notifications.show({
                color: "red",
                title: t("leads.updateErrorTitle"),
                message: t("leads.updateErrorMessage"),
              });
            },
          },
        );
      }}
      includeLeadProcess
      initialValues={
        filterFalsyValues({
          ...data,
          moveInDate: data.moveInDate ? new Date(data.moveInDate) : null,
          nextCallback: data.nextCallback ? new Date(data.nextCallback) : null,
          makeAnAppointment: data.makeAnAppointment
            ? new Date(data.makeAnAppointment)
            : null,
        }) as LeadFormSchema
      }
      actionSection={
        <>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <LeadContractButton lead={data} />
          <SendQuoteButton />
          <ReservationButton />
          <LeadLostButton leadId={id} />
          <ButtonMain
            label={t("leads.phoneCall")}
            icon={<IconPhone size={18} />}
            onClick={() => {
              const phone = "tel:" + data?.mobile;
              window.location.href = phone;
            }}
          />
        </>
      }
      disabledActionSection={
        data.processStage === "Lost" &&
        data.createdOn &&
        new Date(data.createdOn).getTime() >= oneYearAgo.getTime() ? (
          <>
            <ReOpenButton />
          </>
        ) : null
      }
      headerSection={
        <Group>
          <EntityLayout.ViewerComponent
            leadId={data.id!}
            visual={false}
            avatarSize={undefined}
            color="cyan"
          />
        </Group>
      }
    />
  );
}
