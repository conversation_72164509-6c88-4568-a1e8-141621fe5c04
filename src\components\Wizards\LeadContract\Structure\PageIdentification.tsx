import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { useTranslation } from "react-i18next";
import { Box, Button, Center, Flex, Text } from "@mantine/core";

interface PageIdentificationProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
}

export default function PageIdentification({ lead }: PageIdentificationProps) {
  const { t } = useTranslation("features");
  return (
    <Box>
      <Center>
        <Flex direction={"column"} align={"center"}>
          <Text fz={22} fw={600} c={"#282828"}>
            {t("Identification Step")}
          </Text>
          <Text fz={16} fw={300} c={"#282828"}>
            {t("description of this step")}
          </Text>
        </Flex>
      </Center>
      <Center mt={120}>
        <Text fz={24} fw={600} c={"#282828"}>
          {t("scan NFC / OCR / or identify customer later")}
        </Text>
      </Center>
      <Center mt={120}>
        <Button>Identify Person</Button>
      </Center>
    </Box>
  );
}
