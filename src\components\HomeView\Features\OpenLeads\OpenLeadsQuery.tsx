import { useEntityListQuery } from "@/features/entity/queries";
import UseUserBusinessUnit from "@/hooks/businessUnit/useUserBusinessUnit";
import { type Schemas } from "@/types";

export default function OpenLeadsQuery() {
  const { userBusinessUnitId } = UseUserBusinessUnit();

  const callbackStart = new Date(new Date("2024-01-01").setHours(0, 0, 0, 0));
  const callbackEnd = new Date(new Date().setHours(24, 0, 0, 0));

  const filter = [
    userBusinessUnitId ? `businessUnitId == ${userBusinessUnitId}` : null,
    "RecordState == Active",
    "CallCountNotReached >= 0",
    "CallCountNotReached <= 4",
    "(ProcessStage == Appointment || ProcessStage == SpaceTour || ProcessStage == FollowUp)",
    `(NextCallback >= ${callbackStart.toISOString().replace("Z", "")} && NextCallback <= ${callbackEnd.toISOString().replace("Z", "")} || NextCallback == null)`,
    "Priority >= 4",
  ]
    .filter(Boolean)
    .join(" && ");

  const { data } = useEntityListQuery<Schemas["LeadRetrieveDtoPagedList"]>({
    resourcePath: "/api/Leads",
    params: {
      filter: filter,
      orderBy: "Priority",
    },
    queryKey: `leadListHomeStoreManagerOpenLeads`,
  });

  const leads = data?.data ?? [];

  return { leads };
}
