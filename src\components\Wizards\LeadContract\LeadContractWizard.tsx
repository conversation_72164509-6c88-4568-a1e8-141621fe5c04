import { Box } from "@mantine/core";
import { useState } from "react";
import WizardHeader from "../Common/Header/WizardHeader";
import { type Schemas } from "@/types";
import PageStart from "./Structure/PageStart";
import PageIdentification from "./Structure/PageIdentification";
import PageUnitSelection from "./Structure/PageUnitSelection";
import PageVerification from "./Structure/PageVerification";

interface LeadContractWizardProps {
  closeModal?: () => void;
  lead: Schemas["LeadRetrieveDto"];
}

const DEFAULT_TOTAL_PAGES = 3;

export type PageName =
  | "VERIFICATION"
  | "START"
  | "IDENTIFICATION"
  | "UNIT_SELECTION"
  | "COMPLETED";

export default function LeadContractWizard({
  closeModal,
  lead,
}: LeadContractWizardProps) {
  const [pages, setPages] = useState<PageName[]>(["VERIFICATION"]);
  const [totalPages, setTotalPages] = useState(DEFAULT_TOTAL_PAGES);
  const currentPageContent = () => {
    switch (pages.at(-1)) {
      case "VERIFICATION":
        return (
          <PageVerification
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "START":
        return (
          <PageStart
            setPages={setPages}
            setTotalPages={setTotalPages}
            pages={pages}
            lead={lead}
          />
        );
      case "IDENTIFICATION":
        return (
          <PageIdentification
            lead={lead}
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "UNIT_SELECTION":
        return (
          <PageUnitSelection
            lead={lead}
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "COMPLETED":
        return <div> Completed </div>;
      default:
        return <div>Page not found</div>;
    }
  };
  return (
    <Box h={"80vh"}>
      <WizardHeader
        defaultTotalPages={DEFAULT_TOTAL_PAGES}
        setPages={setPages}
        setTotalPages={setTotalPages}
        pages={pages}
        totalPages={totalPages}
        closeModal={closeModal}
      />
      {currentPageContent()}
    </Box>
  );
}
