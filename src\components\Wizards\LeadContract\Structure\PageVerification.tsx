import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { useTranslation } from "react-i18next";
import { Box, Button, Center, Flex, Text } from "@mantine/core";
import { IconBox, IconLogout, IconPencil } from "@tabler/icons-react";

interface PageVerificationProps extends PageProps<PageName> {}

export default function PageVerification({
  setPages,
  pages,
}: PageVerificationProps) {
  const { t } = useTranslation("features");
  return (
    <Box>
      <Center w={"100%"} mt={120}>
        <Flex w={"60%"} direction={"row"} justify={"space-between"} gap={12}>
          <Box flex={1} p={12} style={{ borderRadius: 8, border: "2px solid" }}>
            <Center p={12}>
              <IconBox size={42} />
            </Center>
            <Center p={12}>
              <Text fz={18} fw={700}>
                {t("NFC StorageLogic App")}
              </Text>
            </Center>
          </Box>
          <Box flex={1} p={12} style={{ borderRadius: 8, border: "2px solid" }}>
            <Center p={12}>
              <IconPencil size={42} />
            </Center>
            <Center p={12}>
              <Text fz={18} fw={700}>
                {t("Manual")}
              </Text>
            </Center>
          </Box>
          <Box flex={1} p={12} style={{ borderRadius: 8, border: "2px solid" }}>
            <Center p={12}>
              <IconLogout size={42} />
            </Center>
            <Center p={12}>
              <Text fz={18} fw={700}>
                {t("IDIN")}
              </Text>
            </Center>
          </Box>
        </Flex>
      </Center>
      <Center w={"100%"} mt={40}>
        <Button
          w={"40%"}
          variant="outline"
          onClick={() => {
            setPages([...pages, "UNIT_SELECTION"]);
          }}
        >
          {t("Do This Later")}
        </Button>
      </Center>
    </Box>
  );
}
