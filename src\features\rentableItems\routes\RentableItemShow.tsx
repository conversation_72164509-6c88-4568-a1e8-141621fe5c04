import { <PERSON>Loader } from "@/components/PageLoader";
import { useState } from "react";
import { EntityLayout } from "@/features/entity";
import {
  useEntityDeleteMutation,
  useEntityUpdateMutation,
} from "@/features/entity/mutations";
import { useEntityQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { filterFalsyValues } from "@/utils/filters";
import { Group } from "@mantine/core";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import {
  RentableItemForm,
  type FormSchema,
} from "../components/RentableItemForm";
import { notifications } from "@mantine/notifications";
import { useQueryClient } from "react-query";

export function RentableItemShow() {
  const navigate = useNavigate();
  const queryCache = useQueryClient();
  const { t } = useTranslation("features");
  const { id } = useParams<{ id: string }>();
  const [close, setClose] = useState(false);
  const { mutate: update } = useEntityUpdateMutation<
    Schemas["RentableItem"],
    Schemas["RentableItemCreateDto"]
  >({
    resourcePath: "/api/RentableItems/{id}",
    resourceId: id!,
    queryKey: "rentableItem",
  });
  const [searchParams] = useSearchParams();

  const redirectTo = searchParams.get("redirectTo") ?? "/app/rentableItems";
  const refreshForm = async () => {
    await queryCache.invalidateQueries("rentableItem_" + id);
  };
  const { mutateAsync, isError: isDeleteError } = useEntityDeleteMutation({
    resourcePath: "/api/RentableItems/{id}",
    resourceId: id!,
    queryKey: "rentableItem",
  });

  const {
    data = {},
    isLoading,
    isFetching,
  } = useEntityQuery<Schemas["RentableItem"]>({
    resourcePath: "/api/RentableItems/{id}",
    resourceId: id!,
    queryKey: "rentableItem",
  });

  if (isLoading || isFetching) {
    return <PageLoader />;
  }

  return (
    <RentableItemForm
      isCreate={false}
      title={t("rentableItems.showTitle", { id })}
      initialValues={filterFalsyValues(data) as FormSchema}
      onSubmit={(values) => {
        if (Object.keys(values).length === 0) {
          if (close) {
            navigate(redirectTo);
          } else {
            return;
          }
        }
        update(values as Schemas["RentableItemCreateDto"], {
          onSuccess: () => {
            if (close) {
              navigate(redirectTo);
            } else {
              notifications.show({
                color: "green",
                title: t("notifications.updateSuccessTitle"),
                message: t("notifications.updateSuccessMessage"),
              });
            }
          },
        });
      }}
      actionSection={
        <Group>
          <EntityLayout.SaveButton setClose={setClose} />
          <EntityLayout.SaveAndCloseButton setClose={setClose} />
          <EntityLayout.CreateButton
            to={
              window.location.origin +
              "/" +
              window.location.pathname.split("/")[1] +
              "/" +
              window.location.pathname.split("/")[2] +
              "/create"
            }
          />
          <EntityLayout.RefreshButton clicked={refreshForm} />
          <EntityLayout.DeleteButton
            modalTitle={t("rentableItems.delete", { id })}
            modalContent={t("rentableItems.deleteConfirmation", { id })}
            confirmLabel={t("rentableItems.delete", { id })}
            onClick={async () => {
              await mutateAsync();
              if (!isDeleteError) {
                navigate(redirectTo ?? "/app/rentableItems");
              }
            }}
          />
        </Group>
      }
    />
  );
}
