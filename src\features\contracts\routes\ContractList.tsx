import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import {
  DateRenderer,
  EntityLinkRenderer,
} from "@/components/Table/CellRenderers";
import { Group } from "@mantine/core";
import { type PathKeys, type Schemas } from "@/types";
import { ListCommandsProvider, useListCommands } from "@/hooks/useListCommands";
import {
  GetCreatePath,
  GetResourcePath,
  type InnerListProps,
  type OutterListProps,
} from "@/features/entity/utils";
import { CustomerLookup } from "@/components/Lookup/Features/Customers/CustomerLookup";
import TableRenderer from "@/components/Table/renderers/TableRenderer";

const PATH = "Contracts";

export function ContractListInner({
  resourcePath,
  createPath,
}: InnerListProps) {
  const { t } = useTranslation("features");
  const { tableRef } = useListCommands();

  return (
    <EntityLayout stickyHeader={false}>
      <EntityLayout.TableMantine<
        Schemas["ContractRetrieveDto"],
        Schemas["ContractRetrieveDtoPagedList"]
      >
        tableRef={tableRef}
        resourcePath={resourcePath as PathKeys}
        queryKey="Contract"
        entityPath="contracts"
        title={t("contracts.title")}
        toolbar={
          <Group>
            <EntityLayout.CreateButton to={createPath} />
          </Group>
        }
        redirectTo={window.location.pathname}
        columns={[
          {
            accessorKey: "contractNumber",
            header: t("contracts.contractNumber"),
            filterVariant: "text",
            Cell: (props) => EntityLinkRenderer(props, "contracts"),
          },
          {
            accessorKey: "customer",
            header: t("contracts.customer"),
            ...TableRenderer(CustomerLookup, "customers", ["name"]),
          },
          {
            accessorKey: "createdOn",
            header: t("entity.createdOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
          {
            accessorKey: "signedOn",
            header: t("contracts.signedOn"),
            sortingFn: "datetime",
            filterVariant: "date-range",
            Cell: DateRenderer,
          },
        ]}
        initialSorting={[
          {
            id: "contractNumber",
            desc: true,
          },
        ]}
      />
    </EntityLayout>
  );
}

export function ContractList({
  parentEntityName,
  parentEntityId,
  parentEntityIdParam,
}: OutterListProps) {
  const resourcePath = GetResourcePath(parentEntityName, parentEntityId, PATH);
  const createPath = GetCreatePath(parentEntityId, parentEntityIdParam, PATH);
  return (
    <ListCommandsProvider>
      <ContractListInner resourcePath={resourcePath} createPath={createPath} />
    </ListCommandsProvider>
  );
}
