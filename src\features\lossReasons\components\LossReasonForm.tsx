import { z } from "zod";
import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { Grid, Paper, Switch, TextInput } from "@mantine/core";
import { type ReactNode } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { getDirtyFormFields, recordState } from "@/features/entity/utils";
import { FieldValidation } from "@/components/Elements/Field/FieldValidation";
import { HtmlTemplateLookup } from "@/components/Lookup/Features/HtmlTemplates/HtmlTemplateLookup";

const recordStateEnum = z.enum(recordState as [string]);

const formSchema = z.object({
  name: z.string().nullable(),
  htmlTemplateId: z.string().nullable(),
  htmlTemplate: z.object({}).nullable(),
  recordState: recordStateEnum.refine((value) => !!value),
});

export type FormSchema = z.infer<typeof formSchema>;

interface LossReasonFormProps {
  onSubmit: (values: Partial<FormSchema>) => void;
  actionSection?: ReactNode;
  initialValues?: Partial<FormSchema>;
  title: string;
  isCreate: boolean;
}

export function LossReasonForm({
  onSubmit,
  actionSection,
  initialValues,
  title,
  isCreate,
}: LossReasonFormProps) {
  const { t } = useTranslation("features");
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);

  const form = useForm<FormSchema>({
    initialValues: {
      name: initialValues?.name ?? "",
      htmlTemplateId: initialValues?.htmlTemplateId ?? "",
      htmlTemplate: initialValues?.htmlTemplate ?? null,
      recordState: initialValues?.recordState ?? "",
    },
    validate: zodResolver(formSchema),
  });

  return (
    <form
      onSubmit={form.onSubmit((fields) => {
        const filteredFields = getDirtyFormFields(
          fields,
          isCreate,
          form.isDirty,
        );
        debouncedOnSubmit(filteredFields);
        form.resetDirty();
      })}
    >
      <EntityLayout
        hasUnsavedChanges={form.isDirty()}
        title={title}
        disabledActionSection={true}
        actionSection={actionSection}
      >
        <Grid>
          <Grid.Col span={{ base: 12, md: 12 }}>
            <Paper shadow="xs" p="xs" pt="">
              <FieldValidation isDirty={form.isDirty("name")}>
                <TextInput
                  disabled
                  label={t("lossReasons.name")}
                  {...form.getInputProps("name")}
                  {...{ labelProps: { style: { flex: 0.5 } } }}
                />
              </FieldValidation>
              <FieldValidation isDirty={form.isDirty("htmlTemplateId")}>
                <HtmlTemplateLookup
                  flex={1}
                  mt="sm"
                  label={t("lossReasons.htmlTemplate")}
                  initial={form.getValues().htmlTemplate}
                  initialId={form.getValues().htmlTemplateId}
                  identifier="htmlTemplateIdLossReason"
                  {...form.getInputProps("htmlTemplateId")}
                  {...{ labelProps: { style: { flex: "0.5" } } }}
                />
              </FieldValidation>
              <Switch
                label={
                  form.values.recordState == "Active"
                    ? t("common.active")
                    : t("common.inactive")
                }
                checked={form.values.recordState === "Active"}
                onChange={(event) =>
                  form.setFieldValue(
                    "recordState",
                    event.currentTarget.checked ? "Active" : "Inactive",
                  )
                }
              />
            </Paper>
          </Grid.Col>
        </Grid>
      </EntityLayout>
    </form>
  );
}
