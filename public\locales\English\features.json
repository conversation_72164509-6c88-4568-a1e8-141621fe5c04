{"notifications": {"createSuccessMessage": "Item was created successfully!", "createSuccessTitle": "Success", "updateSuccessTitle": "Item was updated successfully!", "updateSuccessMessage": "Success"}, "advisedProducts": {"createTitle": "Create Advised Product", "createSuccessMessage": "Advised Product created successfully!", "createSuccessTitle": "Success", "delete": "Delete Advised Product", "deleteConfirmation": "Are you sure you want to delete this Advised Product? This action is destructive.", "editTitle": "Edit Advised Product", "lead": "Lead", "price": "Price", "product": "Product", "quantity": "Quantity", "quote": "Quote", "showTitle": "Advised Product", "submit": "Submit", "title": "Advised Products", "totalPrice": "Total Price"}, "calendar": {"week": "Week", "month": "Month", "today": "Today", "agenda": "Agenda", "filters": "Filters", "day": "Day"}, "advisedUnits": {"active": "Active", "aboutToExpire": "About To Expire", "cancelled": "Cancelled", "converted": "Converted", "extended": "Extended", "expired": "Expired", "createSuccessMessage": "Advised Unit created successfully!", "createSuccessTitle": "Success", "businessUnit": "Business Unit", "createTitle": "Create Advised Unit", "delete": "Delete Advised Unit", "deleteConfirmation": "Are you sure you want to delete this Advised Unit? This action is destructive.", "editTitle": "Edit Advised Unit", "electricity": "Electricity", "insurance": "Insurance", "lead": "Lead", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "lighting": "Lighting", "maxPrice": "<PERSON><PERSON>", "minPrice": "<PERSON><PERSON>", "pricePerMonth": "Price Per Month", "pricePerWeek": "Price Per Week", "priceWarning": "Price Per Month must be between Minimum and Maximum Price.", "quote": "Quote", "reservation": "Reservation", "showTitle": "Advised Unit", "submit": "Submit", "title": "Advised Units", "type": "Type", "unit": "Unit", "unitType": "Unit Type"}, "appUserBusinessUnits": {"createTitle": "Assign Business Unit", "delete": "Remove Business Unit", "deleteConfirmation": "Are you sure you want to delete this Business Unit? This action is destructive.", "deselectAll": "Deselect All", "error": "Error", "manageCustomerContacts": "Manage Business Unit", "reset": "Reset", "selectAll": "Select All", "showTitle": "Business Unit", "successTitle": "Success", "title": "Assigned Business Units"}, "appUsers": {"allRoles": "All Roles", "appUser": "App User", "assignedRoles": "Assigned Roles", "businessUnit": "Business Unit", "confirm": "Confirm", "currentApp": "Current App", "currentapp": "Current App", "department": "Department", "division": "Division", "dutch": "Dutch", "editTitle": "Edit App User", "email": "Email", "english": "English", "error": "Error", "firstName": "First Name", "general": "General", "id": "Id", "jobTitle": "Job Title", "language": "Language", "lastName": "Last Name", "lastUserLogin": "Last Login", "last_login": "Last Login", "manageRoles": "Manage Roles", "name": "Name", "noRowsSelected": "Please select atleast one row.", "nothingChangedMessage": "There is nothing to update.", "nothingChangedTitle": "No Changes", "showTitle": "App User", "submit": "Submit", "timeZone": "Timezone", "title": "App Users", "userGroup": "User Group"}, "wizards": {"DoubleOrVisitedAnotherName": "Double or visited under another name", "ProductOrServiceNotAvailable": "Product or service not available", "SituationChangedNoNeedForStorageAnymore": "Situation changed, no need for storage anymore", "AppointmentForMovingVanTrailer": "Appointment for moving van or trailer", "RentedCompetitorBecauseOfPrice": "Rented at competitor because of price", "RentedCompetitorBecauseOfLocation": "Rented at competitor because of location", "LeftBelongingsWithFamily": "Left belongings with family or friendsntype", "TooExpensive": "Too expensive", "WantedToKnowThePrice": "Wanted to know the price", "InformedSomebodyElse": "Informed for somebody else", "ForgotAppointment": "Forgot appointment", "SomethingIntervened": "Something intervened", "NotNeededNow": "Not needed now", "NoShow": {"Title": "Lead did not show up - contact lead to check status", "Label": " Check in with the lead via phone call and choose the next step accordingly", "UpdateStartTimeTitle": "Update Start Time", "UpdateStartTimeDescription": "Lead showed up late", "UpdateStartTimeFooter": "This action will set the appointment start time to new value to accommodate for late entry", "RescheduleTitle": "Reschedule", "RescheduleDescription": "Lead wants to reschedule the appointment", "RescheduleFooter": "This will create a new rescheduled appointment and set the previous one to no show", "CancelTitle": "Cancel", "CancelDescription": "Lead canceled and does not want a new appointment", "CancelFooter": "This will set the appointment as no show, lead will be contacted by sales and service", "NotReachedTitle": "Not reached", "NotReachedDescription": "Lead was not reached via a phone call", "NotReachedFooter": "This will set the appointment as a no show and mark the lead as no show", "NotReachedConfirmButton": "Set as not reached"}, "Completed": {"Title": "Completed!", "Label": "Appointment status updated"}, "SpaceTour": {"Title": "Space Tour Details", "Label": "Fill in required tour details", "ConfirmButton": "Save Tour Data"}, "CancelAppointment": {"Title": "Cancel Scheduled Appointment", "Label": "The lead is not interested in rescheduling and wants to cancel the appointmen - sets it as no show", "ConfirmButton": "Confirm Cancellation"}, "UpdateAppointment": {"Title": "Update Appointment Status", "Label": "Update appointment status based on lead activity", "LeadShowTitle": "Lead is at the location", "LeadShowDescription": "Lead showed up for appointment", "LeadShowFooter": "This action will request space tour form to be filled and set the appointment as completed", "LeadNoShowTitle": "Lead did not show up", "LeadNoShowDescription": "Lead is missing for planned appointment", "LeadNoShowFooter": "A call to the lead will be required to determine further flow", "ConfirmButton": "Confirm adjusted start time"}, "UpdateStartTime": {"Title": "Lead is late", "Label": "Push the appointment start time accordingly", "UpdateTitle": "Adjusted appointment start time", "UpdateLabel": "Move appointment start time to correct value", "ConfirmButton": "Update appointment start time"}, "RescheduleAppointment": {"Title": "Reschedule Appointment", "Label": "Lead wants to reschedule the appointment", "ConfirmButton": "Reschedule appointment", "NewTimeTitle": "New Appointment start time", "NewTimeLabel": "Move appointment start time to correct value", "AgendaTitle": "Agenda", "AgendaLabel": "Scheduled appointments in "}, "QuickAddLead": {"Title": "Quick Add Lead", "Label": "Enter details of a new lead", "ConfirmButton": "Create New Lead", "QuickButtonLabel": "Quick Add New Lead"}}, "appointmentTypeTemplateMappings": {"appointmentType": "Appointment Type", "cancelButton": "Cancel", "editAppointmentTypeTemplateMapping": "Edit Appointment Type Template Mapping", "editTitle": "Edit Appointment Type Template Mapping", "htmlTemplate": "HTML Template", "showTitle": "Appointment Type Template Mapping", "title": "Appointment Types"}, "appointments": {"appointment": "Appointment", "appointmentState": "State", "start": "Start", "end": "End", "nextCallback": "Next Callback", "rescheduledWizard": "Rescheduled From Wizard", "rescheduledWizardReason": "Rescheduled Reason", "priority": "Priority", "callCount": "Call Count", "noShowNotes": "No Show Notes", "appointmentType": "Appointment Type", "appointmentTypeFilter": "Appointment Type Filter", "assignedTo": "Assigned To", "attachment": "Attachment", "attachments": "Attachments", "bankSafe": "<PERSON>ault Tour", "blockCalendar": "Block Calendar", "businessUnit": "Business Unit", "businessUnitTypeFilter": "Business Unit Type Filter", "cancel": "Cancel", "cancelButton": "Cancel", "closeAppointmentButton": "Close Appointment", "confirmCancelled": "Confirm Cancellation of Appointment", "confirmCompleted": "Confirm Completion of Appointment", "confirmNoShow": "Confirm No Show of Appointment", "contact": "Contact", "createdOn": "Created On", "createAppointment": "Create Appointment", "createPhoneCallFlag": "Register a phone call attempt to lead", "createSuccessMessage": "Appointment created and sent successfully!", "createSuccessTitle": "Success", "createTitle": "Create Appointment", "delete": "Delete Appointment", "deleteConfirmation": "Are you sure you want to delete this Appointment? This action is destructive.", "description": "Description", "desk": "Desk", "duration": "Duration", "edit": "Edit", "editAppointment": "Edit Appointment", "editTitle": "Edit Appointment", "email": "Email", "endDate": "End Date", "engineRoomTour": "Engine Room Tour", "followUp": "Follow-Up", "isDefault": "<PERSON>", "lead": "Lead", "leadAndContact": "Leads & Contacts", "movingHelp": "Moving Help", "movingHelpBlockCalendar": "Moving Help - Block Calendar", "movingVan": "Moving Van", "name": "Attachment Name", "ok": "OK", "rescheduleAndSend": "Reschedule & Send", "resetFilters": "Reset All Filters", "roboticStorageTour": "Robotic Storage Tour", "save": "Save", "saveAndSend": "Save & Send", "searchAppointments": "Search Appointments...", "searchBusinessUnits": "Search Business Units...", "selectedBUs": "Selected Business Units", "sendEmail": "Send Email", "setAppointmentCancelled": "<PERSON>cel Appointment", "setAppointmentCompleted": "Complete Appointment", "setAppointmentCompletedFromNoShow": "Change appointment to Completed", "setAppointmentNoShow": "Set appointment as No Show", "setAsCompleted": "Set as Completed", "setAsNoShow": "Set as Completed", "showTitle": "Appointment", "spaceTour": "Space Tour", "startDate": "Start Date", "subject": "Subject", "title": "Appointments", "trailer": "Trailer", "rentableItem": "Rentable Item", "appointmentStatus": "Appointment Status", "open": "Open", "completed": "Completed", "noShow": "No Show", "cancelled": "Cancelled", "rescheduled": "Rescheduled", "changeStatusButton": "Change Appointment Status", "meetingTime": "Meeting Time", "noAppointments": "No Appointments Found", "pendingAppointments": "Pending Appointments", "doneToday": "Done Today", "cancelReasonLabel": "Select Appointment cancel reason", "rescheduleReasonLabel": "Select Appointment reschedule reason", "cancelNotesPlaceholder": "Add notes for the cancellation", "TimeLabel": "Appointment time", "NewTimeLabel": "New appointment time", "BusinessUnitLabel": "Business unit", "PhoneLabel": "Phone number", "MobileLabel": "Mobile number", "RentableItemUnavailable": "Rentable item unavailable"}, "attachments": {"addAttachment": "Add Attachment", "attachments": "Attachments", "deleteAttachment": "Delete Attachment", "deleteCancel": "Cancel", "deleteConfirmation": "Delete", "deleteConfirmationQuestion": "Are you sure you want to delete the attachment", "deleteConfirmationWarning": "This action cannot be undone.", "deleteTitle": "Delete confirmation", "downloadAllLabel": "Download All", "downloadAttachment": "Download Attachment", "downloadLabel": "Download", "noAttachments": "No Attachments", "removeAttachment": "Remove Attachment", "replaceAttachment": "Replace Attachment"}, "audits": {"changes": "Changes", "operation": "Operation", "title": "Audits"}, "businessUnits": {"accessControlType": "Access Control Type", "active": "Active", "administrationCode": "Administration Code", "afoMigrationId": "AFO Migration ID", "bankSafe": "<PERSON><PERSON>", "brivo": "Brivo", "businessUnit": "Business Unit", "businessUnitColor": "Business Unit Color", "campers": "Campers", "carSmallCaravan": "Car / Small Caravan", "cars": "Cars", "city": "City", "code": "Code", "costCentreCode": "Cost Centre Code", "country": "Country", "createTitle": "Create Business Unit", "dateOfOpening": "Date Of Opening", "delete": "Delete Business Unit", "deleteConfirmation": "Are you sure you want to delete this Business Unit? This action is destructive.", "description": "Description", "districtManager": "District Manager", "division": "Division", "driveInHeightUnit": "Drive-In Height Unit", "editTitle": "Edit Business Unit", "email": "Email", "engineRoom": "Engine Room", "hasEngineRoom": "Engine Room", "hasEngineRoomDescription": "Business Unit has an engine room", "hasRoboticStorage": "Robotic Storage", "hasRoboticStorageDescription": "Business Unit is powered by robotic storage", "hasVault": "<PERSON><PERSON>", "hasVaultDescription": "Business Unit has a vault", "iban": "IBAN", "inactive": "Inactive", "isHidden": "Hide Business Unit", "isHiddenDescription": "Business Unit will be hidden from selectors", "isMrBox": "Mr<PERSON><PERSON>", "isMrBoxDescription": "Business Unit offers Mr. Box services", "isSelfStorage": "Self Storage", "isSelfStorageDescription": "Business Unit is self storage", "locationName": "Location Name", "mainPhone": "Main Phone", "manager": "Manager", "name": "Name", "noBusinessUnitFound": "No Business Units Found", "objectID": "Object ID", "parentBusiness": "Parent Business", "parkingLength": "Parking Length", "parkingLotOutside": "Parking Lot Outside", "parkingSpotsType": "Parking Spots Type", "prisma": "Prisma", "recognitionPoints": "Recognition Points", "recordState": "Status", "region": "Region", "roadwayHeight": "Roadway Height", "roboticStorage": "Robotic Storage", "sCAccess": "SC Access", "sectionContacts": "Contact Information", "sectionDetails": "Business Unit Details", "sectionFeatures": "Additional Features", "sectionLocation": "Location Details", "sectionManagement": "Management", "sectionParking": "Parking Details", "sectionTypes": "Unit Types", "selectBusinessUnit": "Select Business Unit", "selfStorage": "Self Storage", "seniorManager": "Senior Manager", "showTitle": "Business Unit", "street": "Street", "submit": "Submit", "title": "Business Units", "truck": "Truck", "type": "Type", "units": "Units", "usesDynamicPricing": "Dynamic Pricing", "usesDynamicPricingDescription": "Business Unit uses dynamic pricing", "usesTKB": "TKB", "usesTKBDescription": "Business Unit uses TKB", "website": "Website", "zip": "Postal Code", "promotionalAction": "Promotional Action", "rent6Get1Free": "Rent 6 months, get the first month for free (16,66% discount)", "rent3Get1Free": "Rent 3 months, get the first month for free (33.33% discount)", "rent2Get1Free": "Rent 2 months, get the first month for free (50% discount)", "tyreChangeDiscount": "50% discount on a tyrechange 'plus'"}, "caseComments": {"case": "Case", "comment": "Comment", "createTitle": "Create Case Comment", "delete": "Delete Case Comment", "deleteConfirmation": "Are you sure you want to delete this Case Comment? This action is destructive.", "editTitle": "Edit Case Comment", "noCaseCommentsFound": "No Case Comments Found", "number": "Number", "showCaseComments": "Show Case Comments", "showTitle": "Case Comment", "submit": "Submit", "text": "Text", "title": "Case Comments", "owner": "Owner"}, "caseReasons": {"createTitle": "Create Case Reason", "editTitle": "Edit Case Reason", "name": "Name", "noCaseReasonsFound": "No Case Reasons Found", "originCategory": "Origin Category", "selectCaseReason": "Select Case Reason", "showComplaintReason": "Show Complaint Reason", "showTitle": "Case Reason", "submit": "Submit", "title": "Case Reasons"}, "downloads": {"addFiles": "Add Files", "removeAllFiles": "Remove All Files", "removeFile": "Remove File", "send": "Send", "sendAll": "Send All", "sendForward": "Send Forward", "subject": "Subject", "to": "To"}, "entityLayout": {"accepted": "Accepted", "new": "New", "show": "Show", "sent": "<PERSON><PERSON>", "edit": "Edit", "delete": "Delete", "save": "Save", "saveAndClose": "Save & Close", "refresh": "Refresh", "closed": "Closed", "active": "Active", "inactive": "Inactive", "disabled": "Disabled", "incomplete": "Incomplete", "checked": "Checked", "inProgress": "In Progress", "reopened": "Reopened", "escalated": "Escalated", "escalationReturned": "Escalation Returned"}, "cases": {"EscalationReturned": "Escalation Returned", "originalBusinessUnitId": "Original Business Unit Id", "replyMailboxes": "Reply Mailboxes", "Standard": "Standard", "to": "To", "Webshop": "Webshop", "active": "Active", "activeCases": "Active Cases", "allCases": "All Cases", "businessUnit": "Business Unit", "cancel": "Cancel", "case": "Case", "caseClose": "Close Case", "caseCloseCancel": "Cancel", "caseCloseConfirm": "Close", "caseCloseConfirmation": "Are you sure you want to close this case?", "caseOpen": "Reopen Case", "caseOpenConfirm": "Open", "caseOpenConfirmation": "Are you sure you want to reopen this case?", "caseOwner": "Case Owner", "caseReasons": "Case Reasons", "caseStartProgress": "Start Handling Case", "caseStartProgressErrorMessage": "Error occured while setting status to in progress...", "caseStartProgressErrorTitle": "Status was not changed!", "caseStartProgressSuccessMessage": "Status was set to in progresss...", "caseStartProgressSuccessTitle": "Status was changed successfully", "casesCloseConfirmation": "Are you sure you want to close the selected cases?", "close": "Close", "closeCasesButton": "Close Cases", "closeCasesCancel": "Cancel", "closeCasesConfirm": "Close Selected Cases", "closeCasesTitle": "Close Cases", "closed": "Closed", "complaintReasons": "Complaint Reasons", "confirm": "Confirm", "confirmEscalationTitle": "Confirm Escalation", "contact": "Contact", "createSuccessMessage": "The Case has been created successfully.", "createSuccessTitle": "Case Created", "createTitle": "Create Case", "delete": "Delete Case", "deleteConfirmation": "Are you sure you want to delete this Case? This action is destructive.", "description": "Description", "emails": "Emails", "escalate": "Escalate Case", "escalated": "Escalated", "escalatedCasesView": "Sales & Service: Escalated Cases", "escalationErrorMessage": "Error occurred during escalation.", "escalationErrorTitle": "Error", "escalationReturned": "Escalation Returned", "escalationSuccessMessage": "Case Escalation was successful.", "escalationSuccessTitle": "Success", "forward": "Forward", "general": "General", "history": "History", "inProgress": "In Progress", "inactive": "Inactive", "inactiveCases": "Inactive Cases", "new": "New", "noCaseFound": "No Cases Found", "number": "Number", "originCategories": "Origin Categories", "originUrl": "Origin URL", "originalBusinessUnit": "Original Business Unit", "originalBusinessUnitIs": "Original Business Unit Id", "origins": "Origins", "recordState": "Record State", "reopened": "Reopened", "reply": "Reply", "replyTemplate": "Template", "returnEscalation": "Return Escalation", "returnEscalationText": "Case will be returned to the original Business Unit {{BusinessUnitCode}}", "selectCase": "Select Case", "sendReply": "Reply", "sendReplyAll": "Reply All", "sendReplyTooltip": "This action will not send an email to the CC and BCC", "senderEmail": "Sender <PERSON><PERSON>", "showTitle": "Case", "standard": "Standard", "status": "Status", "subject": "Subject", "title": "Cases", "updateSuccessMessage": "The Case has been updated successfully.", "updateSuccessTitle": "Case Updated", "webshop": "Webshop", "closedToday": "Closed Today"}, "common": {"active": "Active", "all": "All", "appointments": "Appointments", "callbacks": "Callbacks", "cancel": "Cancel", "confirmNavigation": "Unsaved changes will be lost. Continue?", "contactMoments": "Contact Moments", "create": "Create", "createNewActivity": "Create New Activity", "emails": "Emails", "filterActivities": "Filter Activities", "inactive": "Inactive", "leadEvents": "Lead Events", "leads": "Leads", "no": "No", "phoneCalls": "Phone Calls", "save": "Save", "smsMessages": "SMS Messages", "webForms": "Web Forms", "yes": "Yes", "searchPlaceholder": "Search...", "createdOn": "Created On", "createdBy": "Created By", "modifiedOn": "Modified On", "modifiedBy": "Modified By", "owner": "Owner", "businessUnit": "Business Unit", "status": "Status"}, "complaintReasons": {"createTitle": "C<PERSON> Comp<PERSON>t Reason", "editTitle": "<PERSON> Complaint Reason", "name": "Name", "noComplaintReasonsFound": "No Complaint Reasons Found", "selectComplaintReason": "Select Complaint Reason", "showTitle": "Complaint Reason", "submit": "Submit", "title": "Complaint Reasons"}, "contactMoments": {"comment": "Comment", "contactMoment": "Contact Moment", "createContactMoment": "Create Contact Moment", "lead": "Lead"}, "contactRoles": {"confirm": "Confirm", "contactRole": "Contact Role", "contactRoles": "Contact Roles", "delete": "Delete Contact Role", "deleteConfirmation": "Are you sure you want to delete this Contact Role? This action is destructive.", "deselectAll": "Deselect All", "error": "Error", "isMain": "Main", "manageContactRoles": "Manage Contact Roles", "name": "Name", "no": "No", "noTagsSelected": "No contact roles are selected.", "reset": "Reset", "selectAll": "Select All", "showTitle": "Contact Role", "successMessage": "Tags Updated Successfully", "successTitle": "Success", "title": "Contact Roles", "yes": "Yes", "createTitle": "Create Contact Role"}, "contacts": {"contact": "Contact", "createTitle": "Create Contact", "dateOfBirth": "Date Of Birth", "delete": "Delete Contact", "deleteConfirmation": "Are you sure you want to delete this Contact? This action is destructive.", "editTitle": "Edit Contact", "email": "Email", "firstName": "First Name", "identificationNumber": "Identification Number", "identificationType": "Identification Type", "lastName": "Last Name", "mobile": "Mobile", "noContactsFound": "No Contacts Found", "number": "Number", "phone": "Phone", "reservations": "Reservations", "selectContact": "Select Contact", "showTitle": "Contact", "submit": "Submit", "title": "Contacts"}, "contractLines": {"combinationLock": "Combination Lock", "contractId": "Contract", "contractLine": "Contract Line", "createSuccessMessage": "The Contract Line has been created successfully.", "createSuccessTitle": "Contract Line Created", "createTitle": "Create Contract Line", "delete": "Delete Contract Line", "deleteConfirmation": "Are you sure you want to delete this Contract Line? This action is destructive.", "discount": "Discount", "externalReference": "External Reference", "from": "From", "general": "General", "insuranceEnd": "Insurance End", "insuranceStart": "Insurance Start", "insuranceValue": "Insurance Value", "noContractLinesFound": "No Contract Lines Found", "price": "Price", "quantity": "Quantity", "remarks": "Remarks", "selectContractLine": "Select Contract Line", "showTitle": "Contract Line", "storageType": "Storage Type", "storageValue": "Storage Value", "title": "Contract Lines", "to": "To", "totalPrice": "Total Price", "unitGroup": "Unit Group", "updateSuccessMessage": "The Contract Line has been updated successfully.", "updateSuccessTitle": "Contract Line Updated", "vat": "VAT"}, "contracts": {"cancelReason": "Cancel Reason", "cancelledOn": "Cancelled On", "contact": "Contact", "contract": "Contract", "contractNumber": "Number", "contractStatus": "Contract Status", "createSuccessMessage": "The Contract has been created successfully.", "createSuccessTitle": "Contract Created", "createTitle": "Create Contract", "customer": "Customer", "delete": "Delete Contract", "deleteConfirmation": "Are you sure you want to delete this Contract? This action is destructive.", "firstCancelDate": "First Cancel Date", "from": "From", "general": "General", "invoicePeriodFrom": "Invoice Period From", "invoiceReference": "Invoice Reference", "invoicedUntil": "Invoiced Until", "invoicing": "Invoicing", "invoicingInterval": "Invoicing Interval", "invoicingType": "Invoicing Type", "moveOutDate": "Move Out Date", "paymentMethod": "Payment Method", "remarks": "Remarks", "selectContract": "Select Contract", "showTitle": "Contract", "signedOn": "Signed On", "title": "Contracts", "to": "To", "updateSuccessMessage": "Contract", "updateSuccessTitle": "Contract"}, "countries": {"code": "Code", "countries": "Countries", "country": "Country", "createTitle": "Create Country", "delete": "Delete Country", "deleteConfirmation": "Are you sure you want to delete this Country? This action is destructive.", "editTitle": "Edit Country", "mainPhone": "Main Phone", "name": "Name", "noStorageTypeFound": "No Countries Found", "selectStorageType": "Select Country", "showTitle": "Country", "submit": "Submit", "title": "Countries"}, "customerContacts": {"confirm": "Confirm", "contact": "Contact", "contactRole": "Contact Role", "createTitle": "Customer Contact", "customer": "Customer", "customerContacts": "Customer Contacts", "delete": "Delete Customer Contact", "deleteConfirmation": "Are you sure you want to delete this Customer Contact? This action is destructive.", "deselectAll": "Deselect All", "error": "Error", "manageCustomerContacts": "Manage Customer Contacts", "noTagsSelected": "No contact roles are selected.", "reset": "Reset", "selectAll": "Select All", "showTitle": "Customer Contact", "successMessage": "Tags Updated Successfully", "successTitle": "Success", "title": "Customer Contacts"}, "customerSearch": {"charactersLeft": "characters left...", "moreRecordsAvailable": "More records available, please refine your search...", "noResults": "No records found, please refine your search...", "pleaseSearch": "Please enter a search term:", "searchTitle": "Search Customers"}, "customers": {"address": "Address", "afoMigrationId": "AFO Migration Id", "contact": "Contact", "city": "City", "country": "Country", "createTitle": "Create Customer", "customer": "Customer", "delete": "Delete Customer", "deleteConfirmation": "Are you sure you want to delete this Customer? This action is destructive.", "editTitle": "Edit Customer", "email": "Email", "fax": "Fax", "mobile": "Mobile", "name": "Name", "noCustomersFound": "No Customers Found", "parentCustomer": "Parent Customer", "phone": "Phone", "selectCustomer": "Select Customer", "showTitle": "Customer", "street": "Street", "submit": "Submit", "title": "Customers", "website": "Website", "zip": "Postal Code"}, "dataImport": {"title": "Lead data import tool", "description": "Import leads from a CSV file. The CSV file should contain the following columns: ", "columns": " FirstName, LastName, Mobile, Email, BusinessUnit, Description", "downloadTemplate": "Download template", "dropzoneTitle": "Drag and drop a CSV file here or click to select a file", "dropzoneSelected": "Selected File: ", "importResults": "Import results: ", "messageMissingCSV": "CSV file not found.", "messageSuccess": "Items imported successfully. Processed entities: ", "messageNoItems": "No items processed.", "statusSuccess": "Import Successful", "statusFailed": "Import Failed", "messageUploadFailed": "Error uploading file. Please try again.", "importButton": "Process selected file"}, "emails": {"attachments": "Attachments", "body": "Body", "cc": "CC", "createdOn": "Created On", "delete": "Delete Email", "deleteConfirmation": "Are you sure you want to delete this Email? This action is destructive.", "download": "Download", "editTitle": "<PERSON> Email", "error": "Error", "from": "From", "hideHistory": "Hide History", "name": "Name", "sender": "Sender", "showHistory": "Show History", "showHtml": "Show HTML", "showTitle": "Email", "showVisual": "Show Email", "subject": "Subject", "submit": "Submit", "title": "Emails", "to": "To", "replyTemplate": "Template", "draft": "Draft", "sending": "Sending", "failed": "Failed", "sent": "<PERSON><PERSON>", "cancelled": "Cancelled", "pleaseSelectMailbox": "Please select a mailbox", "selectMailbox": "Select a mailbox", "subjectRequired": "Subject field is required", "toRequired": "To field is required", "completed": "Completed"}, "entity": {"callCount": "Call Count", "cancel": "Cancel", "createCustomView": "Create Custom View", "createNew": "Create New", "createdBy": "Created By", "createdOn": "Created On", "delete": "Delete", "deleteEntity": "Delete Entity", "deleteEntityConfirmation": "Are you sure you want to delete this entity? This action is destructive.", "edit": "Edit", "goBack": "Back", "modifiedBy": "Modified By", "modifiedOn": "Modified On", "movingHelpNorth": "Moving Help North", "movingHelpSouth": "Moving Help South", "movingHelpWest": "Moving Help West", "nextCallback": "Next Callback", "ownerId": "Owner", "refresh": "Refresh", "save": "Save", "saveAndClose": "Save & Close", "setDefaultView": "Set Default View", "setDefaultViewSuccessMessage": "Default view was set successfully", "setDefaultViewTitle": "Changing Default View", "status": "Status", "wizards": "Wizards"}, "htmlTemplates": {"cases": "Cases", "createTitle": "Create Html Template", "delete": "Delete Html Template", "deleteConfirmation": "Are you sure you want to delete this Html Template? This action is destructive.", "dutch": "Dutch", "editTitle": "Edit Html Template", "english": "English", "html": "HTML English", "htmlNL": "HTML Dutch", "htmlTemplate": "Html Template", "marketing": "Marketing", "name": "Name", "service": "Service", "showTitle": "Html Template", "subjectEN": "Subject English", "subjectNL": "Subject Dutch", "submit": "Submit", "templateType": "Template Type", "title": "Html Templates", "replyTo": "Reply To"}, "leadList": {"callListStoreManagerView": "Call List"}, "leads": {"leadLossWarning": "There are open appointments on this lead", "leadLossMessage": "Completing this action will cancel all of the remaining appointments, except for Moving Van, Trailer and Moving Help. Do you wish to continue?", "leadLossConfirm": "Yes, Close", "leadLossExit": "No, Exit", "private": "Private", "business": "Business", "contractWizard": "Contract Wizard", "yes": "Yes", "no": "No", "openLeads": "Open Leads", "activeLeadsPriority2": "Active Leads - Priority 2", "activeLeadsPriority3": "Active Leads - Priority 3", "activeLeadsPriority4": "Active Leads - Priority 4", "activeLeadsPriority5": "Active Leads - Priority 5", "activeLeadsPriority6": "Active Leads - Priority 6", "activeLeadsPriority7": "Active Leads - Priority 7", "openLeadsPriority4": "4+ Priority Leads", "inactiveLeads": "Inactive Leads", "noShows": "No Shows", "activeLeads": "Active Leads", "allLeads": "All Leads", "NationalAccounts": "National Account", "aSAP": "As Soon As Possible", "aboutToExpire": "About To Expire", "noShow": "No Show", "active": "Active", "activities": "Time Line", "activitiesDropDownLabel": "Activities", "addressSection": "Address", "advertisement": "Advertisement", "advisedProducts": "Advised Products", "advisedUnits": "Advised Units", "amount": "Amount", "appointment": "Appointment", "appointments": "Appointments", "approvalForAddressUsage": "Allow To Use Address", "archive": "Archive", "archiveUnit": "Archive Unit", "backToParents": "Back to Parents", "basis": "<PERSON><PERSON>", "boatStorage": "Boot Storage", "budget": "Budget", "businessAddress": "Business Address", "businessUnit": "Business Unit", "callCount": "Call Count", "callCountNotReached": "Call Count Not Reached", "callCountReached": "Call Count Reached", "callSection": "Call Info", "callback": "Callback", "callbackAppointment": "Callback Appointment", "camperStorage": "Camper Storage", "cancel": "Cancel", "cancelReservation": "Cancel Reservation", "cancelled": "Cancelled", "cannotExtendResFromCurrentStatusMessage": "Cannot extend reservation from current status. Please cancel and create a new one.", "cannotExtendResFromCurrentStatusTitle": "Reservation Cannot be Extended", "caravanStorage": "Caravan Storage", "city": "City", "cleanedUp": "Cleaned Up", "close": "Close", "closed": "Closed", "comments": "Comments", "companyName": "Company Name", "competitionLocation": "Competition Location", "competitionPrice": "Competition Price", "confirm": "Confirm", "confirmReOpen": "Do you really want to open a closed Lead?", "confirmReservation": "Do you really want to reserve advised units?", "confirmReservationCancel": "Do you really want to cancel current reservation?", "confirmReservationExtend": "Do you really want to extend current reservation?", "confirmReservationTitle": "Confirm Reservation", "confirmSendQuote": "Please confirm sending the quote", "contactMoment": "Contact Moment", "contract": "Contract", "contractNumberHeader": "Contract Number", "converted": "Converted", "country": "Country", "createSuccessMessage": "The Lead has been created successfully.", "createSuccessTitle": "Lead Created", "createTitle": "Create Lead", "createWalkin": "Create Walk-In", "crisisNursingPension": "To crisis shelter, nursing or elderly home", "currentQuote": "Current Quote", "customDiscount": "Custom Discount", "customerEvent": "Customer Event", "delete": "Delete Lead", "deleteConfirmation": "Are you sure you want to delete this Lead? This action is destructive.", "description": "Description", "discount": "Discount", "discountText": "Discount Text", "distance": "Distance", "doubleOptIn": "Double Opt-In", "dropdownAllTooltipLabel": "Open/Close All", "dutch": "Dutch", "editTitle": "Edit Lead", "email": "Email", "emailHeader": "Email", "emigration": "Emigration", "engineRooms": "Engine Rooms", "engineRooms12": "Engine Rooms 12", "engineRooms6": "Engine Rooms 6", "english": "English", "escalated": "Escalated", "escalationReturned": "Escalation Returned", "existingContactId": "Existing Contact", "existingCustomerId": "Existing Customer", "existingLeadFound": "Existing Lead Found", "expired": "Expired", "extend": "Extend", "extendUntil": "Extend Until", "extended": "Extended", "extraLarge": "Extra Large", "extraSmall": "Extra Small", "extraSpaceHobby": "Extra Space Hobby", "extraSpaceLiveSmaller": "Extra Space Live Smaller", "extraSpaceLiveTogether": "Extra Space Live Together", "extraSpaceSeasonalStorage": "Extra Space Seasonal Storage", "extraSpaceStaging": "Extra Space Staging", "facility": "Facility", "facilityUnit": "Facility Unit", "fax": "Fax", "firstName": "First Name", "fixedTerm36": "Fixed Term 36", "fixedTerm12": "Fixed Term 12", "fixedTerm6": "Fixed Term 6", "followUp": "Follow up", "form": "Form", "foundAnother": "Found Another", "freeStorageGift": "Get your free storage gift", "general": "General", "generalCost": "What is it going to cost?", "googleAdWords": "Google Ad Words", "hasAttachment": "Has Attachments", "history": "History", "homeService": "Home Service", "inProgress": "In Progress", "inactive": "Inactive", "large": "Large", "lastName": "Last Name", "later": "Later", "lead": "Lead", "leadEvent": "Lead Event", "leadLossErrorMessage": "Losing the lead failed. Please see the error.", "leadLossErrorTitle": "Error", "leadLossSuccessMessage": "Success", "leadLossSuccessTitle": "Lead was lost successfully.", "leadLostConfirm": "Confirm loss reason", "leadLostTitle": "Select a reason for losing the Lead", "leadSource": "Lead Source", "leadSourceHeader": "Lead Source", "leftForFamily": "Left For Family", "lettersMail": "Letters Mail", "longStay12to10": "Long Stay 12 to 10", "longStay12to11": "Long Stay 12 to 11", "lossReason": "Loss Reason", "lost": "Lost", "lowestPriceGuarantee": "Lowest Price Guarantee", "mailbox": "Mailbox", "makeAnAppointment": "Make an Appointment", "manageQuotes": "Manage Quotes", "medium": "Medium", "mobile": "Mobile", "mobileHeader": "Mobile", "months0to3": "0-3 months", "months3to6": "3-6 months", "months6to9": "6-9 months", "months9to12": "9-12 months", "motor": "Motor", "motorStorageCost": "What does motor storage cost?", "motorUnitBasis": "Motor Unit Basis", "motorUnitBudget": "Motor Unit Budget", "motorUnitPlus": "Motor Unit Plus", "moveInDate": "Move-In Date", "moveStepperCancel": "Cancel", "moveStepperConfirm": "Move to this step", "moveStepperText": "Do you really want to move lead to selected step?", "moveStepperTitle": "Lead Stepper", "movingCancelled": "Moving Cancelled", "movingHelp": "Moving Help", "movingHouse": "Moving House", "mr": "Mr", "mrBox": "mrBOX", "mrMrs": "Mr / Mrs", "mrs": "Mrs", "mx": "Mx", "nationalAccount": "National Account", "new": "New", "newsletter": "Newsletter", "nextCallback": "Next Callback", "noContact": "No Contact", "noLeadsFound": "No Leads Found", "notReached": "Not Reached", "omnichannelActivate": "Start", "omnichannelDeactivate": "Stop", "oneMonth": "1 Month", "oneWeek": "1 Week", "optIn": "Opt-In", "optInType": "Opt-In Type", "optOut": "Opt-Out", "optOutEmail": "Opt Out Email", "optOutPhone": "Opt Out Phone", "other": "Other", "pOBox": "PO Box", "parkingPlace": "Parking Place", "partner": "Partner", "passingAway": "Passing Away", "personnelUnit": "Personnel Unit", "phone": "Phone", "phoneCall": "Phone Call", "phoneCallAtBU": "Phone Call At Business Unit", "phoneCallAtSalesService": "Phone Call At Sales And Service", "phoneCallStatus": "Phone Call Status", "phoneCallStatusRequiredTitle": "Required Field", "phoneCallStatusRequiredMessage": "Phone Call Status Is Required For Sales & Service Application", "phoneCalls": "Phone Calls", "phoneHeader": "Phone", "plus": "Plus", "postNL": "PostNL", "preferredLanguage": "Preferred Language", "price": "Price", "priority": "Priority", "processStage": "Process Stage", "processStageHeader": "Process Stage", "productType": "Product Type", "promoRentFor4PayFor2": "Promo Rent for 4 Pay for 2", "promotionCode": "Promotion Code", "purchasedList": "Purchased List", "question": "Question", "quotationEmailSent": "Quotation Email Sent", "reOpen": "Re-Open", "reOpenErrorMessage": "Re-Opening of Lead has failed. Please see the error.", "reOpenErrorTitle": "Error", "reOpenMessage": "After Lead is re-opened, it's Loss Reason will be cleared and it's Process Stage be set to New.", "reOpenSuccessMessage": "Lead was re-opened successfully", "reOpenSuccessTitle": "Success", "reached": "Reached", "recordState": "Record State", "recordStateHeader": "Record State", "redirectedToExistingLead": "You were redirected to an existing lead.", "refurbishment": "Refurbishment", "rentAsBusiness": "Rent as Business", "rentFor3PayFor2": "Rent for 3 Pay for 2", "rentFor4PayFor3": "Rent for 4 Pay for 3", "rentFor6PayFor5": "Rent for 6 Pay for 5", "rentVehicle": "Rent Vehicle", "reopened": "Reopened", "reservation": "Reservation", "reservationAlreadyExtendedMessage": "Reservation is already extended. Please cancel it and create a new one.", "reservationAlreadyExtendedTitle": "Reservation Already Extended", "reservationCancelErrorMessage": "Cancelling reservation was not successful, please see the error.", "reservationCancelErrorTitle": "Reservation Cancelling Failed", "reservationCancelSuccessMessage": "Reservation was canceled successfully.", "reservationCancelSuccessTitle": "Reservation Canceled", "reservationErrorMessage": "Reservation was not successful, please see the error.", "reservationErrorTitle": "Reservation Failed", "reservationExtendErrorMessage": "Extending reservation was not successful, please see the error.", "reservationExtendErrorTitle": "Reservation Extension Failed", "reservationExtendSuccessMessage": "Reservation was extended successfully.", "reservationExtendSuccessTitle": "Reservation Extended", "reservationFieldsRequiredMessage": "Please fill Reservation Start and Reservation Until correctly.", "reservationFieldsRequiredTitle": "Required <PERSON>", "reservationStart": "Reservation Start", "reservationStatus": "Reservation Status", "reservationSuccessMessage": "Selected Units were reserved.", "reservationSuccessTitle": "Reservation Created", "reservations": "Reservations", "reserveUnits": "Reserve Units", "reservedUntil": "Reserved Until", "salesLetter": "Sales Letter", "salutation": "Salutation", "savor": "Savor", "scooterStorage": "Scooter Storage", "selectLead": "Select Lead", "selfService": "Self Service", "send": "Send", "sendEmail": "Send Email", "sendNewQuote": "Send New Quote", "sendQuoteErrorMessage": "Sending Quote has failed. Please see the error.", "sendQuoteErrorTitle": "Error", "sendQuoteSuccessMessage": "Quo<PERSON> was sent successfully.", "sendQuoteSuccessTitle": "Success", "separation": "Separation", "showTitle": "Lead", "sixMonths": "6 Months", "sizeOfUnit": "Size Of Unit", "small": "Small", "sms": "SMS Message", "social": "Social", "soldEverything": "Sold Everything", "spaceTour": "Space Tour", "spaceTourRemarks": "Space Tour Remarks", "startWithin": "Start Within", "step": "Step", "stockStorage": "Stock Storage", "storageCalculator": "Storage Calculator", "storageDuration": "Storage Duration", "storageSpace": "Storage Space", "storageType": "Storage Type", "storageUnitL": "Storage Unit Large", "storageUnitM": "Storage Unit Medium", "storageUnitReason": "Storage Unit Reason", "storageUnitS": "Storage Unit Small", "storageUnitXL": "Storage Unit Extra Large", "storageUnitXS": "Storage Unit Extra Small", "storageValue": "Storage Value", "street": "Street", "submit": "Submit", "supersize": "Supersize", "temporarySolutionCanceled": "Temporary Solution Canceled", "threeMonths": "3 Months", "tireStorageBasis": "Tire Storage Basis", "tireStorageBudget": "Tire Storage Budget", "tireStoragePlus": "Tire Storage Plus", "title": "Leads", "tooExpensiveBudget": "Too Expensive Budget", "tooExpensiveCheaper": "Too Expensive Cheaper", "tooExpensiveWebsite": "Too Expensive Website", "transportation": "Transportation", "twoMonths": "2 Months", "twoWeeks": "2 Weeks", "type": "Type", "unitSize": "Unit Size", "unknown": "Unknown", "updateProcessStageSuccessMessage": "The Process Stage has been updated successfully.", "updateProcessStageSuccessTitle": "Process Stage Updated", "updateSuccessMessage": "The Lead has been updated successfully.", "updateSuccessTitle": "Lead Updated", "volume": "Volume", "waitingForStudentHome": "Waiting for Student Home", "walkIn": "Walk-In", "walkInErrorMessage": "Walk-In creation encountered an error.", "walkInErrorTitle": "Error", "walkInSuccesstitle": "Succes", "walkInSucessMessage": "Walk-In was created successfully.", "wantedPrice": "Wanted Price", "webForm": "WebForm", "webformDetails": "Webform Details", "webformTitle": "Webform Title", "webshopSelfService": "Webshop Self Service", "website": "Website", "won": "Won", "wrongNumber": "Wrong Number", "zip": "Zip", "callDue": "Call Due", "callbackTime": "Callback Time", "pendingCallbacks": "Pending Callbacks", "reachedToday": "Reached Today", "recentlyViewedLeads": "Recently viewed Leads", "newLeads": "New Leads", "spaceTourLeads": "Space Tour Leads", "wonLeadsToday": "Won Leads Today", "lostLeadsToday": "Lost Leads Today"}, "leadEventEntries": {"triggeredBy": "Triggered By", "new": "New", "walkIn": "Walk-In", "lost": "Lost", "reopened": "Reopened", "won": "Won"}, "lookup": {"nothingFound": "Nothing found..."}, "table": {"noRecordsToDisplay": "No records to display", "noResultsFound": "No results found", "sortByColumnAsc": "Sort by {column} ascending", "sortByColumnDesc": "Sort by {column} descending", "sortedByColumnAsc": "Sorted by {column} ascending", "sortedByColumnDesc": "Sorted by {column} descending", "filterByColumn": "Filter by {column}", "filteringByColumn": "Filtering by {column}", "showHideColumns": "Show/Hide Columns", "showAllColumns": "Show All Columns", "hideColumn": "Hide Column", "toggleSelectAll": "Toggle Select All", "toggleFullScreen": "Toggle Full Screen", "rowsPerPage": "Rows per page"}, "lossReasons": {"Already a tenant": "Already a tenant", "Distance Too Far": "Distance Too Far", "Don't call me, I'll call you": "Don't call me, I'll call you", "Duplicate": "Duplicate", "Forgot Appointment": "Forgot Appointment", "General question location/transport": "General question location/transport", "Informed for somebody else": "Informed for somebody else", "Marketing": "Marketing", "No Show": "No Show", "Not reached after 6 attempts": "Not reached after 6 attempts", "Other solution": "Other solution", "Product or service not available": "Product or service not available", "Rented at competitor": "Rented at competitor", "Sales": "Sales", "Sold everything": "Sold everything", "Something Intervened": "Something Intervened", "Spam": "Spam", "Test": "Test", "Too expensive": "Too expensive", "Wants to know price": "Wants to know price", "Wishes not to be contacted": "Wishes not to be contacted", "Wrong information": "Wrong information", "Wrong number": "Wrong number", "editTitle": "Edit Loss Reason", "htmlTemplate": "HTML Template", "name": "Name", "price": "Price", "product": "Product", "showTitle": "Loss Reason", "submit": "Submit", "title": "Loss Reasons", "active": "Active", "inactive": "Inactive"}, "mailbox": {"businessUnit": "Business Unit", "createTitle": "Create Mailbox", "delete": "Delete Mailbox", "deleteConfirmation": "Are you sure you want to delete this Mailbox? This action is destructive.", "description": "Description", "editTitle": "Edit Mailbox", "email": "Email", "name": "Name", "noMailboxFound": "No Mailboxes Found", "selectMailbox": "Select Mailbox", "showTitle": "Mailbox", "submit": "Submit", "title": "Mailboxes"}, "omnichannel": {"createAppointment": "Create Appointment", "createCall": "Create Call", "fullNameIsEmpty": "Full Name Is Empty", "refresh": "Refresh <PERSON>", "activeUsers": "Active Omnichannel Users", "personalStatus": "Personal Status"}, "originCategories": {"code": "Code", "createTitle": "Create Origin Category", "editTitle": "Edit Origin Category", "name": "Name", "noOriginCategoriesFound": "No Origin Categories Found", "selectOriginCategory": "Select Origin Category", "showTitle": "Origin Category", "submit": "Submit", "title": "Origin Categories"}, "origins": {"category": "Category", "createTitle": "Create Origin", "editTitle": "Edit Origin", "name": "Name", "noOriginsFound": "No Origins Found", "selectOrigin": "Select Origin", "showTitle": "Origin", "submit": "Submit", "title": "Origins"}, "phoneCalls": {"autoDescription": "Created automatically after losing a lead.", "blockCalendar": "Block Calendar", "businessUnit": "Business Unit", "callback": "Callback", "callbackDate": "Callback Date", "cancelButton": "Cancel", "closePhoneCallButton": "Close Phone Call", "confirmCallback": "Create Callback?", "confirmCancelled": "Close Phone Call as Cancelled?", "confirmNotReached": "Close Phone Call as Not Reached?", "confirmReached": "Close Phone Call as Reached?", "contact": "Contact", "createCallback": "Create Callback", "createPhoneCall": "Create Phone Call", "createStateSuccessMessage": "Phone Call Created", "createStateSuccessTitle": "Success", "createTitle": "Create Phone Call", "delete": "Delete Phone Call", "deleteConfirmation": "Are you sure you want to delete this Phone Call? This action is destructive.", "description": "Description", "desk": "Desk", "editCallback": "Edit Callback", "editPhoneCall": "Edit Phone Call", "editTitle": "Edit Phone Call", "endDate": "End Date", "lead": "Lead", "movingVan": "Moving Van", "notReached": "Not Reached", "ok": "OK", "phoneCallStatus": "Status", "reached": "Reached", "setPhoneCallCancelled": "Cancel", "setPhoneCallCompleted": "Complete", "setPhoneCallNotReached": "Not Reached", "setPhoneCallReached": "Reached", "showTitle": "Phone Call", "spaceTour": "Space Tour", "startDate": "Start Date", "subject": "Subject", "title": "Phone Calls", "trailer": "Trailer", "updateStateSuccessMessage": "Phone Call Updated", "updateStateSuccessTitle": "Success", "callbackOn": "Callback On", "open": "Open", "cancelled": "Cancelled"}, "prices": {"allPrices": "All Prices", "activePrices": "Active Prices", "inactivePrices": "Inactive Prices", "contact": "Price", "createTitle": "Create Price", "delete": "Delete Contact", "deleteConfirmation": "Are you sure you want to delete this Price? This action is destructive.", "editTitle": "Edit Price", "from": "From", "maxPrice": "<PERSON><PERSON>", "minPrice": "<PERSON><PERSON>", "noPricesFound": "No Prices Found", "pricePerMonth": "Price Per Month", "selectPrice": "Select Price", "showTitle": "Price", "submit": "Submit", "title": "Prices", "to": "To", "unit": "Unit"}, "products": {"afoMigrationId": "AFO Migration Id", "allowPriceChange": "Allow Price Change", "createTitle": "Create Product", "delete": "Delete Product", "deleteConfirmation": "Are you sure you want to delete this Product? This action is destructive.", "editTitle": "Edit Product", "name": "Name", "no": "No", "price": "Price", "product": "Product", "showTitle": "Product", "submit": "Submit", "title": "Products", "yes": "Yes"}, "quotes": {"accepted": "Accepted", "active": "Active", "cancelQuote": "<PERSON><PERSON> Quote", "cancelled": "Cancelled", "createTitle": "Create Quote", "createdOn": "Created On", "delete": "Delete Quote", "deleteConfirmation": "Are you sure you want to delete this Quote? This action is destructive.", "denied": "Denied", "denyQuote": "<PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "draft": "Draft", "editTitle": "Edit Quote", "emails": "Emails", "expirationDate": "Expiration Date", "expired": "Expired", "lead": "Lead", "name": "Name", "number": "Number", "owner": "Owner", "quote": "Quote", "revised": "Revised", "sent": "<PERSON><PERSON>", "showTitle": "Quote", "status": "Status", "submit": "Submit", "title": "Quotes", "totalMonthlyPrice": "Total Monthly Price", "totalOneTimeFee": "Total One-Time Fee"}, "refundProducts": {"createTitle": "Create Refund Product", "delete": "Delete Refund Product", "deleteConfirmation": "Are you sure you want to delete this refund product?", "price": "Price", "product": "Product", "quantityToReturn": "Qty. to Return", "refund": "Refund", "showTitle": "Refund Product", "stillUsableQuantity": "Still Usable Qty.", "title": "Refund Products", "totalPrice": "Total Price"}, "refunds": {"bankAccount": "Bank Account", "businessUnit": "Business Unit", "case": "Case", "checked": "Checked", "closed": "Closed", "contact": "Contact", "createTitle": "Create Refund", "creditCard": "Credit Card", "customer": "Customer", "customerBankAccountNumber": "Customer Bank Account Number", "delete": "Delete Refund", "deleteConfirmation": "Are you sure you want to delete this refund?", "description": "Description", "handlingDate": "Handling Date", "iDeal": "iDeal", "incomplete": "Incomplete", "incompleteProduct": "Incomplete Product", "internalRemarks": "Internal Remarks", "klarna": "<PERSON><PERSON><PERSON>", "new": "New", "normalUseProductDamage": "Normal Use Product Damage", "number": "Number", "orderNumber": "Order Number", "other": "Other", "payPal": "PayPal", "paymentMethod": "Payment Method", "pinOnLocation": "Pin On Location", "postNLProductDamage": "Post NL Product Damage", "proofOfOrder": "Proof Of Order", "quality": "Quality", "returnReason": "Return Reason", "returningUnused": "Returning Unused", "showTitle": "Refund", "status": "Status", "title": "Refunds", "totalOrderAmount": "Total Order Amount", "totalRefundedAmount": "Refunded Amount", "updateSuccessMessage": "Refund has been updated successfully.", "updateSuccessTitle": "Update Successful"}, "reservations": {"createTitle": "Create Reservation", "delete": "Delete Reservation", "deleteConfirmation": "Are you sure you want to delete this Reservation? This action is destructive.", "editTitle": "Edit Reservation", "lead": "Lead", "reservationStatus": "Reservation Status", "reservedUntil": "Reserved Until", "sendEmail": "Send Email", "showTitle": "Reservation", "start": "Start", "status": "Status", "submit": "Submit", "title": "Reservations", "until": "Until", "active": "Active", "aboutToExpire": "About To Expire", "cancelled": "Cancelled", "converted": "Converted", "extended": "Extended", "expired": "Expired"}, "RentableItems": {"movingVan": "Moving Van", "trailer": "Trailer", "available": "Available", "diesel": "Diesel", "petrol": "Petrol", "hybrid": "Hybrid"}, "rentableItems": {"createTitle": "Create Rentable Item", "rentableItem": "Rentable Item", "availability": "Availability", "available": "Available", "businessUnit": "Business Unit", "inMaintenance": "In Maintenance", "notAvailable": "Not Available", "licensePlate": "License Plate", "brand": "Brand", "model": "Model", "null": "", "fuelType": "Fuel Type", "diesel": "Diesel", "petrol": "Petrol", "hybrid": "Hybrid", "deleteConfirmation": "Are you sure you want to delete this Rentable Item? This action is destructive.", "editTitle": "Edit Rentable Item", "showTitle": "Rentable Item", "submit": "Submit", "type": "Type", "movingVan": "Moving Van", "trailer": "Trailer", "title": "Rentable Items", "loadingSpaceLength": "Length", "loadingSpaceHeight": "Height", "loadingSpaceWidth": "<PERSON><PERSON><PERSON>", "loadingSpaceVolume": "Volume", "maxLoad": "<PERSON>", "loadingSpaceSection": "Loading Space", "updateSuccessMessage": "Rentable Item has been updated successfully.", "updateSuccessTitle": "Rentable Item Updated"}, "roles": {"description": "Description", "error": "Error", "id": "Id", "name": "Name", "noRolesSelected": "No roles are selected.", "showTitle": "Role", "successMessage": "Roles Updated Successfully", "successTitle": "Success", "title": "Roles"}, "settings": {"appointmentEmailTemplate": "Appointment Em<PERSON> Template", "callbackSalesEmailTemplate": "Callback Sales & Service Email Template", "callbackStoreManagerEmailTemplate": "Callback Store Manager <PERSON><PERSON>late", "dateFormat": "Date Format", "delete": "Delete Setting", "deleteConfirmation": "Are you sure you want to delete this Setting? This action is destructive.", "editTitle": "Edit Setting", "general": "General", "minimumDepositForUnits": "Minimum Deposit For Units", "name": "Name", "noShowAppointmentEmailTemplate": "No Show Appointment Em<PERSON> Template", "omnichannelPriority": "Omnichannel Priority", "quoteAttachmentTemplate": "Quote At<PERSON><PERSON><PERSON> Template", "quoteEmailTemplate": "Quote <PERSON><PERSON>", "quoteExpiration": "Quote Expiration", "reservationEmailTemplate": "Reservation Email Template", "salesAndServiceGroup": "Sales And Service Group", "sendEmails": "Enable Email Sending", "setting": "Setting", "showTitle": "Setting", "submit": "Submit", "templateMappings": "Template Mappings", "title": "Settings"}, "smsMessages": {"body": "Body", "delete": "Delete SMS Message", "deleteConfirmation": "Are you sure you want to delete this SMS Message? This action is destructive.", "editTitle": "Edit SMS Message", "externalId": "External Id", "lead": "Lead", "name": "Name", "recipients": "Recipients", "showTitle": "SMS Message", "sms": "SMS Message", "subject": "Subject", "submit": "Submit", "title": "SMS Messages", "createdOn": "Created On", "sentOn": "<PERSON><PERSON>", "draft": "Draft", "sending": "Sending", "failed": "Failed", "sent": "<PERSON><PERSON>", "cancelled": "Cancelled"}, "storageTypes": {"Archive": "Archive", "Art": "Art", "Car": "Car", "Caravan": "Caravan", "Electronics": "Electronics", "Household Goods": "Household Goods", "Household Goods And Inventory": "Household Goods And Inventory", "Inventory": "Inventory", "Inventory Stock": "Inventory Stock", "Mail": "Mail", "Motorcycle": "Motorcycle", "Moving Boxes": "Moving Boxes", "Other": "Other", "Tire Storage": "Tire Storage", "Trailer": "Trailer", "Webshop": "Webshop", "code": "Code", "createTitle": "Create Storage Type", "delete": "Delete Storage Type", "deleteConfirmation": "Are you sure you want to delete this Storage Type? This action is destructive.", "editTitle": "Edit Storage Type", "mainPhone": "Main Phone", "name": "Name", "noStorageTypeFound": "No Storage Types Found", "selectStorageType": "Select Storage Type", "showTitle": "Storage Type", "storageType": "Storage Type", "storageTypes": "Storage Types", "submit": "Submit", "title": "Storage Types"}, "tags": {"confirm": "Confirm", "delete": "Delete Tag", "deleteConfirmation": "Are you sure you want to delete this Tag? This action is destructive.", "deselectAll": "Deselect All", "error": "Error", "manageTags": "Manage Tags", "name": "Name", "noTagsSelected": "No tags are selected.", "reset": "Reset", "selectAll": "Select All", "showTitle": "Tag", "successMessage": "Tags Updated Successfully", "successTitle": "Success", "title": "Tags", "unitTags": "Unit Tags", "createTitle": "Create Tag", "tags": "Tag"}, "unitTags": {"confirm": "Confirm", "delete": "Delete Tag", "deleteConfirmation": "Are you sure you want to delete this Tag? This action is destructive.", "deselectAll": "Deselect All", "error": "Error", "manageTags": "Manage Tags", "name": "Name", "noTagsSelected": "No tags are selected.", "reset": "Reset", "selectAll": "Select All", "showTitle": "Unit Tag", "successMessage": "Tags Updated Successfully", "successTitle": "Success", "title": "Tags", "unitTags": "Unit Tags", "createTitle": "Create Unit Tag"}, "unitTypes": {"code": "Code", "contractRemark": "Contract Remark", "contractReport": "Contract Report", "editTitle": "Unit Type", "minimumInsuranceValue": "Minimum Insurance Value", "moveBusFreeHour": "Move Bus Free Hours", "moveBusFreeKm": "Move Bus Free Km's", "name": "Name", "proposedInsuranceValue": "Proposed Insurance Value", "showTitle": "Unit Type", "title": "Unit Types", "unitHeight": "Height", "unitLength": "Length", "unitVolume": "Volume", "unitWidth": "<PERSON><PERSON><PERSON>", "createTitle": "Create Unit Type"}, "units": {"area": "Area", "available": "Available", "available24Hours": "Available 24 Hours", "awaitingHandover": "Awaiting Handover", "basement": "Basement", "blocked": "Blocked", "businessUnit": "Business Unit", "contractCancelled": "Contract Cancelled", "createTitle": "Unit", "delete": "Delete Business Unit", "deleteConfirmation": "Are you sure you want to delete this Unit? This action is destructive.", "distanceToElevator": "Distance to Elevator", "editTitle": "Edit Unit", "email": "Email", "floor": "Floor", "floor0": "Floor 0", "floor1": "Floor 1", "floor2": "Floor 2", "floor3": "Floor 3", "floor4": "Floor 4", "floor5": "Floor 5", "floor6": "Floor 6", "floor7": "Floor 7", "floor8": "Floor 8", "floor9": "Floor 9", "height": "Height", "inMaintenance": "In Maintenance", "inUse": "In Use", "internalUse": "Internal Use", "length": "Length", "lengthHeader": "Length m", "mainPhone": "Main Phone", "moveOut": "Move Out", "noUnitFound": "No Units Found", "notInUse": "Not in Use", "number": "Number", "parking": "Parking", "parkingInside": "Parking Inside", "prices": "Prices", "reserved": "Reserved", "selectUnit": "Select Unit", "showTitle": "Unit", "status": "Status", "submit": "Submit", "tags": "Tags", "title": "Units", "unit": "Unit", "unitCode": "Unit Code", "unitId": "Unit Id", "unitType": "Unit Type", "volume": "Volume", "volumeHeader": "Volume m³", "width": "<PERSON><PERSON><PERSON>", "widthHeader": "Width m", "maxPrice": "<PERSON><PERSON>", "minPrice": "<PERSON><PERSON>", "pricePerMonth": "Price Per Month"}, "userGroups": {"addUser": "Add User", "businessUnit": "Business Unit", "confirm": "Confirm", "deleteConfirm": "Are you sure you want to delete the selected records?", "error": "Error", "groupOwner": "Group Owner", "noRowsSelected": "Please select atleast one row.", "removeUser": "Remove User", "salesAndService": "Sales & Service", "showTitle": "User Group", "title": "User Groups", "updateSuccessMessage": "User Updated Successfully", "updateSuccessTitle": "Update Success", "userGroupType": "Type"}, "webformEntries": {"clientID": "Client ID", "comments": "Comments", "email": "Email", "entryId": "Entry Id", "firstName": "First Name", "gclid": "GCLID", "lastName": "Last Name", "leadSource": "Lead Source", "mobile": "Mobile", "preferredLanguage": "Preferred Language", "price": "Price", "productType": "Product Type", "referrer": "<PERSON><PERSON><PERSON>", "startWithin": "Start Within", "step": "Step", "trackingId": "Tracking Id", "transportation": "Transportation", "transportationRequest": "Transportation Request", "unitSize": "Unit Size", "utm_campaign": "UTM Campaign", "utm_content": "UTM Content", "utm_medium": "UTM Medium", "utm_source": "UTM Source", "utm_term": "UTM Term", "webformDetails": "Webform Details", "webformEntries": "Webform Entries", "webformEntry": "Webform Entry", "webformTitle": "Webform Title"}, "home": {"hello": "Hello,", "welcome": "Welcome to the Storage Logic", "quote": "\"People don’t buy storage, they buy space to make life easier. Show them how!\"", "scheduleAppointment": "Schedule Appointment", "newLead": "New Lead", "newCase": "New Case", "noCallbacks": "No Callbacks Found", "noAppointments": "No Appointments Found", "noRecentLeads": "No Recent Leads Found", "noActiveCases": "No Active Cases Found", "noOmnichannelUsers": "No Active Omnichannel Users Found", "noPriorityLeads": "No Priority Leads Found", "missedCallbacks": "Missed Callbacks"}}