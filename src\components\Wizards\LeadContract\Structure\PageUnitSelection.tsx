import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Center,
  Flex,
  Loader,
  Paper,
  ScrollArea,
  Stack,
  Text,
} from "@mantine/core";
import { IconCheck, IconPlus } from "@tabler/icons-react";
import { useEntityListQuery } from "@/features/entity/queries";
import { UnitBox } from "../Common/UnitBox";

interface PageUnitSelectionProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
}

export default function PageUnitSelection({ lead }: PageUnitSelectionProps) {
  const { t } = useTranslation("features");

  const { data, isLoading } = useEntityListQuery<
    Schemas["AdvisedUnitRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/AdvisedUnits",
    queryKey: `units`,
    params: {
      filter: `businessUnitId == ${lead?.businessUnitId} && leadId == ${lead?.id}`,
      orderBy: "code",
      desc: false,
    },
  });

  const units: Schemas["AdvisedUnitRetrieveDto"][] = data?.data ?? [];

  return (
    <Box>
      <Center mt={20}>
        <Flex direction={"row"} w={"70%"} justify={"space-between"} gap={12}>
          <Paper
            flex={3}
            p={24}
            style={{ borderRadius: 8, border: "1px solid" }}
          >
            <Flex h={"90%"} direction={"column"} gap={8} justify={"start"}>
              <Text fz={18} fw={700}>
                {t("Selected Units")}
              </Text>
              {isLoading && (
                <Center h={"80%"}>
                  <Loader size={"xl"} />
                </Center>
              )}
              {!isLoading && units.length > 0 && (
                <ScrollArea.Autosize mah={"50vh"}>
                  <Stack gap="md">
                    {units.map((unit) => (
                      <UnitBox key={unit.id ?? unit.id} unit={unit} />
                    ))}
                  </Stack>
                </ScrollArea.Autosize>
              )}
            </Flex>
            <Box w={"100%"} mt={12}>
              <Button w={"100%"} variant="outline" leftSection={<IconPlus />}>
                {t("Add Unit")}
              </Button>
            </Box>
          </Paper>
          <Paper
            flex={1}
            p={24}
            style={{ borderRadius: 8, border: "1px solid" }}
          >
            <Text fz={18} fw={700}>
              {t("Summary")}
            </Text>
            {isLoading && (
              <Center h={"80%"}>
                <Loader size={"xl"} />
              </Center>
            )}
            {!isLoading && units.length > 0 && (
              <ScrollArea.Autosize mah={"50vh"}>
                <Stack gap="xs" mt={8}>
                  {units.map((unit) => (
                    <Box
                      key={unit.id ?? unit.id}
                      style={{ borderRadius: 8, border: "1px solid" }}
                      p={12}
                    >
                      <Flex direction="column" gap="xs">
                        <Flex direction="row" justify="space-between">
                          <Text fz={14} fw={400} c={"#282828"}>
                            {unit.unit?.unitCode}
                          </Text>
                          <Text fz={14} fw={400} c={"#282828"}>
                            {unit.pricePerMonth}
                          </Text>
                        </Flex>
                        <Flex direction="row" justify="space-between">
                          <Text fz={14} fw={400} c={"#282828"}>
                            {t("Accessories")}
                          </Text>
                          <Text fz={14} fw={400} c={"#282828"}>
                            {unit.unit?.minPrice}
                          </Text>
                        </Flex>
                        <Flex direction="row" justify="space-between">
                          <Text fz={14} fw={400} c={"#282828"}>
                            {t("Deposit")}
                          </Text>
                          <Text fz={14} fw={400} c={"#282828"}>
                            {unit.pricePerMonth}
                          </Text>
                        </Flex>
                      </Flex>
                    </Box>
                  ))}
                </Stack>
              </ScrollArea.Autosize>
            )}

            <Box
              w={"100%"}
              mt={12}
              p={16}
              style={{ border: "1px solid", borderRadius: 8 }}
            >
              <Flex direction={"column"} gap={8}>
                <Flex direction={"row"} justify={"space-between"} gap={8}>
                  <Text fz={14} fw={400} c={"#282828"}>
                    {t("Taxes (btw)")}
                  </Text>{" "}
                  <Text fz={14} fw={400} c={"#282828"}>
                    {t("79.8")}
                  </Text>
                </Flex>
                <Flex direction={"row"} justify={"space-between"} gap={8}>
                  <Text fz={18} fw={600} c={"#282828"}>
                    {t("Total")}
                  </Text>
                  <Text fz={18} fw={600} c={"#282828"}>
                    {t("380")}
                  </Text>
                </Flex>
              </Flex>
            </Box>
            <Box w={"100%"} mt={12}>
              <Button w={"100%"} variant="primary" leftSection={<IconCheck />}>
                {t("Confirm Selected Units")}
              </Button>
            </Box>
          </Paper>
        </Flex>
      </Center>
    </Box>
  );
}
