import { Loader } from "@/components/Elements/Loader";
import { useEntityListQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { Center, Select } from "@mantine/core";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

interface EmailTemplatesProps {
  setEmailBody: (value: string) => void;
  setSubject: (value: string) => void;
  subject?: string | null;
  templateType: Schemas["TemplateTypeEnum"];
}

export default function EmailTemplates({
  setEmailBody,
  subject,
  setSubject,
  templateType,
}: EmailTemplatesProps) {
  const { t } = useTranslation("features");
  const [template, setTemplate] = useState("Standard");
  const { data, isLoading } = useEntityListQuery<
    Schemas["HtmlTemplateRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/HtmlTemplates",
    queryKey: `htmlTemplates`,
    params: {
      filter: `TemplateType == ${templateType}`,
    },
  });

  const emailTemplates = data?.data;
  useEffect(() => {
    if (emailTemplates == undefined) return;
    const templateToUse = emailTemplates.find((x) => x.name == template);
    if (templateToUse) {
      setEmailBody(templateToUse.html ?? "");
      if (subject == "" || subject == null || subject == undefined) {
        setSubject(templateToUse.subjectNL ?? "");
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [emailTemplates]);

  if (isLoading) {
    return (
      <Center>
        <Loader />
      </Center>
    );
  }

  const formattedTemplates = emailTemplates?.map((template) => {
    return {
      value: template.name!,
      label: t("emails." + template.name),
    };
  });

  return (
    <Select
      label={t("emails.replyTemplate")}
      data={formattedTemplates}
      value={template}
      onChange={(value: string | null) => {
        if (value) {
          setTemplate(value);
          setSubject(
            emailTemplates?.filter((x) => x.name == value)[0]!.subjectNL ?? "",
          );
          setEmailBody(
            emailTemplates?.filter((x) => x.name == value)[0]!.htmlNL ?? "",
          );
        }
      }}
      flex={2}
    />
  );
}
