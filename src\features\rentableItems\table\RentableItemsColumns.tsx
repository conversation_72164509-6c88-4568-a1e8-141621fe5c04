import TableRenderer from "@/components/Table/renderers/TableRenderer";
import { useTranslation } from "react-i18next";
import { type MRT_ColumnDef, type MRT_RowData } from "mantine-react-table";
import { BusinessUnitLookup } from "@/components/Lookup/Features/BusinessUnits/BusinessUnitLookup";
import { FuelType, RentableItemType } from "@/types/enums";
import { lowerCaseNthLetter } from "@/utils/filters";
import { type ComboboxData } from "@mantine/core";

export function RentableItemsColumns() {
  const { t } = useTranslation("features");
  const columns: MRT_ColumnDef<MRT_RowData>[] = [
    {
      accessorKey: "brand",
      header: t("rentableItems.brand"),
      filterVariant: "text",
    },
    {
      accessorKey: "model",
      header: t("rentableItems.model"),
      filterVariant: "text",
    },
    {
      accessorKey: "licensePlate",
      header: t("rentableItems.licensePlate"),
      filterVariant: "text",
    },
    {
      accessorKey: "type",
      header: t("rentableItems.type"),
      filterVariant: "multi-select",
      Cell: ({ cell }) =>
        t("rentableItems." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: RentableItemType as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "fuelType",
      header: t("rentableItems.fuelType"),
      filterVariant: "multi-select",
      Cell: ({ cell }) =>
        t("rentableItems." + lowerCaseNthLetter(cell.getValue<string>())),
      mantineFilterSelectProps: {
        data: FuelType as ComboboxData | undefined,
      },
    },
    {
      accessorKey: "businessUnit",
      header: t("rentableItems.businessUnit"),
      ...TableRenderer(BusinessUnitLookup, "businessUnits", ["code"]),
    },
  ];
  return columns;
}
