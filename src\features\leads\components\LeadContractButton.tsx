import LeadContractWizard from "@/components/Wizards/LeadContract/LeadContractWizard";
import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { type Schemas } from "@/types";
import { Modal } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { IconToiletPaper } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

interface LeadContractButtonProps {
  lead: Schemas["LeadRetrieveDto"];
}

export default function LeadContractButton({ lead }: LeadContractButtonProps) {
  const [opened, { open, close }] = useDisclosure(true);
  const { t } = useTranslation("features");

  const handleOpen = () => {
    open();
  };
  return (
    <>
      <ButtonMain
        label={t("leads.contractWizard")}
        icon={<IconToiletPaper size={18} />}
        onClick={handleOpen}
      />
      <Modal
        opened={opened}
        withCloseButton={false}
        onClose={close}
        size={"100%"}
        centered
      >
        <LeadContractWizard closeModal={close} lead={lead} />
      </Modal>
    </>
  );
}
