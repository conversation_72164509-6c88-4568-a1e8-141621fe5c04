import {
  type ComboboxItem,
  type ComboboxLikeRenderOptionInput,
  Box,
} from "@mantine/core";
import { type TFunction } from "i18next";
import {
  type MRT_ColumnFiltersState,
  type MRT_FilterOption,
  type MRT_ColumnFilterFnsState,
  type MRT_ColumnDef,
  type MRT_RowData,
  type MRT_SortingState,
} from "mantine-react-table";
import { PossiblePriorities } from "./constants";
import { lowerCaseNthLetter } from "@/utils/filters";

// Operator mapping (adjust based on your backend needs)
const getOperator = (operator: MRT_FilterOption | undefined): string => {
  switch (operator) {
    case "contains":
      return "@=*";
    case "equals":
      return "==*";
    case "notEquals":
      return "!=*";
    case "startsWith":
      return "_=*";
    case "endsWith":
      return "_-=*";
    default:
      return "@=*"; // Default or throw error
  }
};

const getBetweenOperator = (
  operator: MRT_FilterOption | undefined,
  field: string,
  values: unknown[],
  appendAnd = true,
): string => {
  let filter = "";
  const [val1, val2] = values;

  const isValidValue = (val: unknown) =>
    val !== null &&
    val !== undefined &&
    (typeof val === "object" ||
      typeof val === "string" ||
      typeof val === "number");

  switch (operator) {
    case "between":
      if (isValidValue(val1))
        filter += `${appendAnd ? " && " : " "}${field} > ${JSON.stringify(val1)}`;
      if (isValidValue(val2))
        filter += `${isValidValue(val1) ? " && " : appendAnd ? " && " : " "}${field} < ${JSON.stringify(val2)}`;
      break;
    case "betweenInclusive":
      if (isValidValue(val1))
        filter += `${appendAnd ? " && " : " "}${field} >= ${JSON.stringify(val1)}`;
      if (isValidValue(val2))
        filter += `${isValidValue(val1) ? " && " : appendAnd ? " && " : " "}${field} <= ${JSON.stringify(val2)}`;
      break;
    default:
      filter = "";
  }
  return filter;
};

export const getFilterString = (
  columnFilters: MRT_ColumnFiltersState,
  columnFilterFns: MRT_ColumnFilterFnsState,
  columns: MRT_ColumnDef<MRT_RowData>[],
): string => {
  let filterString = "";

  columnFilters.forEach((columnFilter) => {
    if (!columnFilter.id) return;

    const column = columns.find((c) => c.id === columnFilter.id);
    const operator = columnFilterFns[columnFilter.id];

    if (!column) return;

    // Special handling for nextCallback date range (example)
    if (
      columnFilter.id === "nextCallback" &&
      column.filterVariant === "date-range" &&
      Array.isArray(columnFilter.value)
    ) {
      filterString += ` && ( ${getBetweenOperator(operator, columnFilter.id, columnFilter.value, false)} || ${columnFilter.id} == null )`;
      return;
    }

    if (operator === "empty") {
      filterString += ` && (${columnFilter.id} == null || ${columnFilter.id} == "")`;
    } else if (operator === "notEmpty") {
      filterString += ` && ${columnFilter.id} != null && ${columnFilter.id} != ""`;
    } else {
      switch (column.filterVariant) {
        case "text":
          filterString += ` && ${columnFilter.id} ${getOperator(operator)} ${JSON.stringify(columnFilter.value)}`;
          break;
        case "date-range":
        case "range":
          if (Array.isArray(columnFilter.value)) {
            filterString += getBetweenOperator(
              operator,
              columnFilter.id,
              columnFilter.value,
            );
          }
          break;
        case "multi-select": {
          let multiFilter = "";
          if (Array.isArray(columnFilter.value)) {
            const values = columnFilter.value;
            values.forEach((value) => {
              if (value && typeof value === "string") {
                multiFilter +=
                  (operator == "equals" ? " || " : " && ") +
                  columnFilter.id +
                  " " +
                  getOperator(operator) +
                  " " +
                  JSON.stringify(value);
              }
            });
          }
          if (
            multiFilter.startsWith(" || ") ||
            multiFilter.startsWith(" && ")
          ) {
            filterString += " && (" + multiFilter.slice(4) + ")";
          }
          break;
        }
        case "select":
          if (
            columnFilter.value !== null &&
            columnFilter.value !== undefined &&
            typeof columnFilter.value === "string"
          ) {
            filterString += ` && ${columnFilter.id} ${getOperator(operator)} ${JSON.stringify(columnFilter.value)}`;
          }
          break;
        default: // Assuming Lookup columns filter by id
          if (columnFilter.value !== null && columnFilter.value !== undefined) {
            filterString += ` && ${columnFilter.id}.id ${getOperator(operator)} ${JSON.stringify(columnFilter.value)}`;
          }
          break;
      }
    }
  });

  return filterString.startsWith(" && ") ? filterString.slice(4) : filterString;
};

// Filter Render Option (moved from main component)
export const filterRenderOption = (
  item: ComboboxLikeRenderOptionInput<ComboboxItem>,
  t: TFunction<"features", undefined>, // Pass t function
  entityPath: string, // Pass entityPath
) => {
  const label = item.option.label;

  // Assuming PossiblePriorities and entityPath are available or passed down
  const shouldTranslate = !PossiblePriorities.includes(label);

  return (
    <Box w="100%" ta={"center"}>
      {shouldTranslate
        ? t(entityPath + "." + lowerCaseNthLetter(label))
        : label}
    </Box>
  );
};

export const getQueryParams = (
  pageIndex: number,
  pageSize: number,
  sorting: MRT_SortingState,
  filterString: string,
  searchBy?: string,
) => ({
  pageNumber: pageIndex,
  orderBy: sorting[0]?.id,
  desc: sorting[0]?.desc,
  pageSize: pageSize,
  filter: filterString || undefined,
  searchBy: searchBy || undefined,
});

export const getDefaultColumnFilterFns = (
  columns: MRT_ColumnDef<MRT_RowData>[],
): MRT_ColumnFilterFnsState => {
  return Object.fromEntries(
    columns.reduce(
      (
        results: [string, MRT_FilterOption][],
        { accessorKey, filterVariant },
      ) => {
        if (accessorKey) {
          let defaultFn: MRT_FilterOption | undefined;
          switch (filterVariant) {
            case "text":
              defaultFn = "contains";
              break;
            case "date-range":
            case "range":
            case "date":
              defaultFn = "betweenInclusive";
              break;
            case "multi-select":
            case "select":
              defaultFn = "equals";
              break;
            // Add default for lookup if needed, maybe 'equals'
            default:
              defaultFn = "equals";
              break; // Default for lookups etc.
          }
          if (defaultFn) {
            results.push([accessorKey, defaultFn]);
          }
        }
        return results;
      },
      [],
    ),
  ) as MRT_ColumnFilterFnsState;
};

export const enrichColumnsWithFilterOptions = (
  columns: MRT_ColumnDef<MRT_RowData>[],
): MRT_ColumnDef<MRT_RowData>[] => {
  return columns.map((column) => {
    switch (column.filterVariant) {
      case "text":
        column.columnFilterModeOptions = [
          "contains",
          "empty",
          "notEmpty",
          "equals",
          "notEquals",
          "startsWith",
          "endsWith",
        ];
        break;
      case "multi-select":
      case "select":
        // Note: 'empty'/'notEmpty' might require backend changes or different filter logic if enums aren't nullable strings.
        column.columnFilterModeOptions = [
          "equals",
          "notEquals" /* , "empty", "notEmpty" */,
        ];
        break;
      // Date/Range variants often don't need explicit mode options as 'betweenInclusive' is common.
      // Default (incl. Lookup)
      default:
        column.columnFilterModeOptions = [
          "equals",
          "notEquals" /* , "empty", "notEmpty" */,
        ];
        break;
    }
    return column;
  });
};
