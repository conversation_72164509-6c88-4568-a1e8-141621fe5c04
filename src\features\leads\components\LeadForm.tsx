import { EntityLayout } from "@/features/entity";
import { useTranslation } from "react-i18next";
import { useDebounceCallback } from "usehooks-ts";
import { Fieldset, Tabs } from "@mantine/core";
import { zodResolver } from "mantine-form-zod-resolver";
import { ProcessStage } from "@/types/enums";
import { LeadProcessStage } from "../components/LeadProcessStage";
import { type ReactNode } from "react";
import { type Schemas } from "@/types";
import { getDirtyFormFields } from "@/features/entity/utils";
import { useNavigate, useParams } from "react-router-dom";
import { GeneralTab } from "./Tabs/GeneralTab";
import { AppointmentsTab } from "./Tabs/AppointmentsTab";
import { SpaceTourTab } from "./Tabs/SpaceTourTab";
import { HistoryTab } from "./Tabs/HistoryTab";
import {
  type LeadFormSchema,
  getLeadFormSchema,
  LeadFormProvider,
  useLeadForm,
} from "../providers/form";

interface LeadFormProps {
  includeLeadProcess?: boolean;
  onSubmit: (values: Partial<LeadFormSchema>) => void;
  updateProcessStage?: (value: Schemas["ProcessStage"]) => void;
  actionSection?: ReactNode;
  disabledActionSection?: ReactNode;
  headerSection?: ReactNode;
  initialValues?: Partial<LeadFormSchema>;
  title: string;
  leadId?: string;
  isCreate: boolean;
}

export function LeadForm({
  includeLeadProcess = false,
  onSubmit,
  initialValues,
  actionSection = null,
  disabledActionSection = null,
  headerSection = null,
  updateProcessStage,
  title,
  leadId,
  isCreate,
}: LeadFormProps) {
  const { id, tabValue } = useParams();
  const debouncedOnSubmit = useDebounceCallback(onSubmit, 500);
  const { t } = useTranslation("features");

  const navigate = useNavigate();
  const form = useLeadForm({
    initialValues: {
      firstName: initialValues?.firstName ?? "",
      lastName: initialValues?.lastName ?? "",
      email: initialValues?.email ?? "",
      mobile: initialValues?.mobile ?? "",
      salutation: initialValues?.salutation ?? "Mx",
      phone: initialValues?.phone ?? "",
      companyName: initialValues?.companyName ?? "",
      webformTitle: initialValues?.webformTitle ?? "",
      webformDetails: initialValues?.webformDetails ?? "",
      discountText: initialValues?.discountText ?? "",
      storageValue: initialValues?.storageValue ?? null,
      spaceTourRemarks: initialValues?.spaceTourRemarks ?? "",
      callCount: initialValues?.callCount ?? 0,
      callCountReached: initialValues?.callCountReached ?? 0,
      callCountNotReached: initialValues?.callCountNotReached ?? 0,
      quotationEmailSent: initialValues?.quotationEmailSent ?? false,
      moveInDate: initialValues?.moveInDate ?? null,
      nextCallback: initialValues?.nextCallback ?? null,
      reservationStart: initialValues?.reservationStart ?? null,
      reservedUntil: initialValues?.reservedUntil ?? null,
      rentAsBusiness: initialValues?.rentAsBusiness ?? null,
      transportation: initialValues?.transportation ?? null,
      discount: initialValues?.discount ?? null,
      storageDuration: initialValues?.storageDuration ?? null,
      storageUnitReason: initialValues?.storageUnitReason ?? null,
      processStage: initialValues?.processStage ?? "New",
      optOutPhone: initialValues?.optOutPhone ?? false,
      description: initialValues?.description ?? "",
      type: initialValues?.type ?? "Private",
      startWithin: initialValues?.startWithin ?? "Later",
      productType: initialValues?.productType ?? "StorageSpace",
      leadSource: initialValues?.leadSource ?? "",
      sizeOfUnit: initialValues?.sizeOfUnit ?? null,
      businessUnitId: initialValues?.businessUnitId ?? "",
      businessUnit: initialValues?.businessUnit ?? null,
      existingContactId: initialValues?.existingContactId ?? "",
      existingContact: initialValues?.existingContact ?? null,
      existingCustomerId: initialValues?.existingCustomerId ?? "",
      existingCustomer: initialValues?.existingCustomer ?? null,
      contractId: initialValues?.contractId ?? "",
      contract: initialValues?.contract ?? null,
      recordState: initialValues?.recordState ?? "",
      comments: initialValues?.comments ?? "",
      promotionCode: initialValues?.promotionCode ?? "",
      fax: initialValues?.fax ?? "",
      city: initialValues?.city ?? "",
      zip: initialValues?.zip ?? "",
      street: initialValues?.street ?? "",
      countryId: initialValues?.countryId ?? "",
      country: initialValues?.country ?? null,
      approvalForAddressUsage: initialValues?.approvalForAddressUsage ?? false,
      storageTypeId: initialValues?.storageTypeId ?? "",
      storageType: initialValues?.storageType ?? null,
      preferredLanguage: initialValues?.preferredLanguage ?? "Dutch",
      optInType: initialValues?.optInType ?? "OptIn",
      step: initialValues?.step ?? "",
      price: initialValues?.price ?? "",
      unitSize: initialValues?.unitSize ?? "",
      volume: initialValues?.volume ?? null,
      amount: initialValues?.amount ?? null,
      lossReasonId: initialValues?.lossReasonId ?? "",
      lossReason: initialValues?.lossReason ?? null,
      makeAnAppointment: initialValues?.makeAnAppointment ?? null,
    },
    validate: zodResolver(getLeadFormSchema(t)),
  });

  const activeProcessStageIndex = ProcessStage.indexOf(
    form.values.processStage,
  );
  const isFormDisabled = form.getInputProps("recordState").value === "Inactive";

  const handleStepClick = (index: number) => {
    if (updateProcessStage) {
      updateProcessStage(ProcessStage[index]! as Schemas["ProcessStage"]);
      form.setFieldValue("processStage", ProcessStage[index] ?? "New");
    }
  };

  const handleTabChange = (value: string | null) =>
    navigate(`/app/leads/${id}/${value}`);

  return (
    <LeadFormProvider form={form}>
      <form
        onSubmit={form.onSubmit((fields) => {
          const filteredFields = getDirtyFormFields(
            fields,
            isCreate,
            form.isDirty,
          );
          debouncedOnSubmit(filteredFields);
          form.resetDirty();
        })}
      >
        <EntityLayout
          disabled={isFormDisabled}
          title={title}
          hasUnsavedChanges={form.isDirty()}
          actionSection={actionSection}
          disabledActionSection={disabledActionSection}
          headerSection={headerSection}
          recordState={t(
            (form.getInputProps("recordState").value as string) ?? "",
          )}
        >
          {includeLeadProcess && (
            <LeadProcessStage
              onStepClick={handleStepClick}
              activeStageIndex={activeProcessStageIndex}
              lossReason={
                (form.getValues().lossReason as Schemas["LossReason"])?.name
              }
            />
          )}
        </EntityLayout>
      </form>
      <Tabs
        ml={10}
        value={tabValue ?? "general"}
        defaultValue="general"
        onChange={handleTabChange}
      >
        <Tabs.List style={{ pointerEvents: "auto", fontWeight: "bold" }}>
          <Tabs.Tab value="general">{t("leads.general")}</Tabs.Tab>
          <Tabs.Tab value="find-need" disabled={!leadId}>
            {t("leads.appointments")}
          </Tabs.Tab>
          <Tabs.Tab value="space-tour" disabled={!leadId}>
            {t("leads.spaceTour")}
          </Tabs.Tab>
          <Tabs.Tab value="history" disabled={!leadId}>
            {t("leads.history")}
          </Tabs.Tab>
        </Tabs.List>
        <Fieldset>
          <Tabs.Panel value="general">
            {(tabValue === "general" || tabValue === undefined) && (
              <GeneralTab leadId={leadId} />
            )}
          </Tabs.Panel>
          <Tabs.Panel value="find-need" style={{ pointerEvents: "auto" }}>
            {tabValue === "find-need" && (
              <AppointmentsTab
                leadId={leadId}
                businessUnitId={form.getValues().businessUnitId}
              />
            )}
          </Tabs.Panel>
          <Tabs.Panel value="space-tour">
            {tabValue === "space-tour" && <SpaceTourTab leadId={leadId} />}
          </Tabs.Panel>
          <Tabs.Panel value="history">
            {tabValue === "history" && <HistoryTab leadId={leadId} />}
          </Tabs.Panel>
        </Fieldset>
      </Tabs>
    </LeadFormProvider>
  );
}
